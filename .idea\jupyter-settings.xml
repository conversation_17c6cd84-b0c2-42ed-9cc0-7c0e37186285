<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JupyterPersistentConnectionParameters">
    <option name="knownRemoteServers">
      <list>
        <JupyterConnectionParameters>
          <option name="authType" value="notebook" />
          <option name="token" value="b4df8ad5ff08a90119889e77c71c7eaf3d11ba6a208db28b" />
          <option name="urlString" value="http://localhost:8888" />
          <authParams2>
            <map>
              <entry key="token" value="b4df8ad5ff08a90119889e77c71c7eaf3d11ba6a208db28b" />
            </map>
          </authParams2>
        </JupyterConnectionParameters>
      </list>
    </option>
    <option name="moduleParameters">
      <map>
        <entry key="$PROJECT_DIR$/.idea/FAKE.iml">
          <value>
            <JupyterConnectionParameters>
              <option name="authType" value="notebook" />
              <option name="token" value="b4df8ad5ff08a90119889e77c71c7eaf3d11ba6a208db28b" />
              <option name="urlString" value="http://localhost:8888" />
              <authParams2>
                <map>
                  <entry key="token" value="b4df8ad5ff08a90119889e77c71c7eaf3d11ba6a208db28b" />
                </map>
              </authParams2>
            </JupyterConnectionParameters>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>