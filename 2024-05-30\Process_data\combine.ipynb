{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据集已保存为 train.tsv、test.tsv 和 valid.tsv\n"]}], "source": ["import pandas as pd\n", "import re\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 读取 CSV 文件\n", "fraud_df = pd.read_csv('fraud.csv')\n", "nonfraud_df = pd.read_csv('nonfraud.csv')\n", "\n", "# 定义提取数字的函数\n", "def extract_number(row):\n", "    numbers = re.findall(r'/d+', str(row))\n", "    return ' '.join(numbers)\n", "\n", "# 提取数字并添加为新列\n", "fraud_df['numbers'] = fraud_df.apply(lambda row: extract_number(row), axis=1)\n", "nonfraud_df['numbers'] = nonfraud_df.apply(lambda row: extract_number(row), axis=1)\n", "\n", "# 添加标签\n", "fraud_df['label'] = 1\n", "nonfraud_df['label'] = 0\n", "\n", "# 合并数据\n", "combined_df = pd.concat([fraud_df, nonfraud_df], ignore_index=True)\n", "\n", "# 删除 'url' 列\n", "if 'url' in combined_df.columns:\n", "    combined_df = combined_df.drop(columns=['url'])\n", "\n", "# 打乱数据\n", "combined_df = combined_df.sample(frac=1, random_state=42).reset_index(drop=True)\n", "\n", "# 按照 7:2:1 比例划分数据集\n", "train_df, temp_df = train_test_split(combined_df, test_size=0.3, random_state=42)\n", "test_df, valid_df = train_test_split(temp_df, test_size=1/3, random_state=42)\n", "\n", "# 保存为 TSV 文件\n", "train_df.to_csv('train.tsv', sep='\\t', index=False)\n", "test_df.to_csv('test.tsv', sep='\\t', index=False)\n", "valid_df.to_csv('valid.tsv', sep='\\t', index=False)\n", "\n", "print(\"数据集已保存为 train.tsv、test.tsv 和 valid.tsv\")\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件已成功分割为训练集、测试集和验证集\n"]}], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 读取CSV文件\n", "df = pd.read_csv('C:/Users/<USER>/Downloads/CHECKED-master/处理后数据集/output2.csv')\n", "\n", "# 打乱数据\n", "df = df.sample(frac=1, random_state=42).reset_index(drop=True)\n", "\n", "# 分割数据集：70%训练集，20%测试集，10%验证集\n", "train_data, temp_data = train_test_split(df, test_size=0.3, random_state=42)\n", "test_data, val_data = train_test_split(temp_data, test_size=1/3, random_state=42)\n", "\n", "# 保存分割后的数据集\n", "train_data.to_csv('train.csv', index=False)\n", "test_data.to_csv('test.csv', index=False)\n", "val_data.to_csv('val.csv', index=False)\n", "\n", "print(\"CSV文件已成功分割为训练集、测试集和验证集\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "TEXTGCN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}