{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-07-11T23:56:05.302511Z", "start_time": "2025-07-11T23:56:05.289119Z"}, "collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["44\n"]}], "source": ["print(\"44\")"]}], "metadata": {"kernelspec": {"display_name": "py_39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 0}