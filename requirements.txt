aiohttp==3.9.5
aiosignal==1.3.1
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1698341106958/work
async-timeout==4.0.3
attrs==23.2.0
backcall @ file:///home/<USER>/feedstock_root/build_artifacts/backcall_1592338393461/work
certifi==2022.12.7
charset-normalizer==2.1.1
click==8.1.7
colorama @ file:///home/<USER>/feedstock_root/build_artifacts/colorama_1666700638685/work
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1710320294760/work
datasets==2.19.0
debugpy @ file:///C:/b/abs_c0y1fjipt2/croot/debugpy_1690906864587/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work
dill==0.3.8
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1698579936712/work
fightingcv-attention==1.0.0
filelock==3.9.0
frozenlist==1.4.1
fsspec==2024.3.1
gensim==4.3.2
huggingface-hub==0.22.2
idna==3.4
importlib_metadata @ file:///home/<USER>/feedstock_root/build_artifacts/importlib-metadata_1710971335535/work
ipykernel @ file:///D:/bld/ipykernel_1708996677248/work
ipython @ file:///D:/bld/ipython_1680185618122/work
ipywidgets==8.1.2
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1696326070614/work
Jinja2==3.1.2
joblib==1.4.0
jupyter_client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1710255804825/work
jupyter_core @ file:///D:/bld/jupyter_core_1669775233774/work
jupyterlab_widgets==3.0.10
keras-nlp==0.8.2
MarkupSafe==2.1.3
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1660814786464/work
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1705850609492/work
networkx==3.2.1
nltk==3.8.1
numpy==1.26.4
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/packaging_1710075952259/work
pandas==2.1.4
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1712320355065/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1602536217715/work
prompt-toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1702399386289/work
psutil @ file:///C:/Windows/Temp/abs_b2c2fd7f-9fd5-4756-95ea-8aed74d0039flsd9qufz/croots/recipe/psutil_1656431277748/work
pure-eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1642875951954/work
pyarrow==16.0.0
pyarrow-hotfix==0.6
Pygments @ file:///home/<USER>/feedstock_root/build_artifacts/pygments_1700607939962/work
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1709299778482/work
pytz==2024.1
pywin32==227
PyYAML==6.0.1
pyzmq @ file:///C:/b/abs_89aq69t0up/croot/pyzmq_1705605705281/work
regex==2023.12.25
requests==2.28.1
safetensors==0.4.3
scikit-learn==1.3.2
scipy==1.10.1
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
smart-open==7.0.4
spacy==3.7.4
stack-data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1669632077133/work
sympy==1.12
tax2vec==0.25
tensorflow-gpu==2.6.0
threadpoolctl==3.4.0
tokenizers==0.19.1
torch==2.1.0+cu121
torchaudio==2.1.0+cu121
torchdata==0.7.0
torchtext==0.16.0
torchvision==0.16.0+cu121
tornado @ file:///D:/bld/tornado_1656937934674/work
tqdm==4.66.2
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1710254411456/work
transformers==4.40.1
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1712329955671/work
tzdata==2024.1
urllib3==1.26.13
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1704731205417/work
widgetsnbextension==4.0.10
wrapt==1.16.0
xxhash==3.4.1
yarl==1.9.4
zipp @ file:///home/<USER>/feedstock_root/build_artifacts/zipp_1695255097490/work
