{"cells": [{"cell_type": "code", "execution_count": 264, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 2.1.0+cu121\n", "cuda\n"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "worksapce = 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "# Define the data loaders for training and validation\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 265, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.autograd import Variable\n", "\n", "\n", "def squash(inputs, axis=-1):\n", "    norm = torch.norm(inputs, p=2, dim=axis, keepdim=True)\n", "    scale = norm**2 / (1 + norm**2) / (norm + 1e-8)\n", "    return scale * inputs\n", "\n", "\n", "class DenseCapsule(nn.Module):\n", "    def __init__(self, in_num_caps, in_dim_caps, out_num_caps, out_dim_caps, routings=3):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.in_num_caps = in_num_caps\n", "        self.in_dim_caps = in_dim_caps\n", "        self.out_num_caps = out_num_caps\n", "        self.out_dim_caps = out_dim_caps\n", "        self.routings = routings\n", "        self.weight = nn.Parameter(0.01 * torch.randn(out_num_caps, in_num_caps, out_dim_caps, in_dim_caps))\n", "\n", "    def forward(self, x):\n", "        # x.size=[batch, in_num_caps, in_dim_caps]\n", "        # expanded to    [batch, 1,            in_num_caps, in_dim_caps,  1]\n", "        # weight.size   =[       out_num_caps, in_num_caps, out_dim_caps, in_dim_caps]\n", "        # torch.matmul: [out_dim_caps, in_dim_caps] x [in_dim_caps, 1] -> [out_dim_caps, 1]\n", "        # => x_hat.size =[batch, out_num_caps, in_num_caps, out_dim_caps]\n", "        x_hat = torch.squeeze(torch.matmul(self.weight, x[:, None, :, :, None]), dim=-1)\n", "\n", "        # In forward pass, `x_hat_detached` = `x_hat`;\n", "        # In backward, no gradient can flow from `x_hat_detached` back to `x_hat`.\n", "        x_hat_detached = x_hat.detach()\n", "\n", "        # The prior for coupling coefficient, initialized as zeros.\n", "        # b.size = [batch, out_num_caps, in_num_caps]\n", "        b = Variable(torch.zeros(x.size(0), self.out_num_caps, self.in_num_caps)).cuda()\n", "\n", "        assert self.routings > 0, 'The \\'routings\\' should be > 0.'\n", "        for i in range(self.routings):\n", "            # c.size = [batch, out_num_caps, in_num_caps]\n", "            c = F.softmax(b, dim=1)\n", "\n", "            # At last iteration, use `x_hat` to compute `outputs` in order to backpropagate gradient\n", "            if i == self.routings - 1:\n", "                # c.size expanded to [batch, out_num_caps, in_num_caps, 1           ]\n", "                # x_hat.size     =   [batch, out_num_caps, in_num_caps, out_dim_caps]\n", "                # => outputs.size=   [batch, out_num_caps, 1,           out_dim_caps]\n", "                outputs = squash(torch.sum(c[:, :, :, None] * x_hat, dim=-2, keepdim=True))\n", "                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat))  # alternative way\n", "            else:  # Otherwise, use `x_hat_detached` to update `b`. No gradients flow on this path.\n", "                outputs = squash(torch.sum(c[:, :, :, None] * x_hat_detached, dim=-2, keepdim=True))\n", "                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat_detached))  # alternative way\n", "\n", "                # outputs.size       =[batch, out_num_caps, 1,           out_dim_caps]\n", "                # x_hat_detached.size=[batch, out_num_caps, in_num_caps, out_dim_caps]\n", "                # => b.size          =[batch, out_num_caps, in_num_caps]\n", "                b = b + torch.sum(outputs * x_hat_detached, dim=-1)\n", "\n", "        return torch.squeeze(outputs, dim=-2)\n", "\n", "\n", "class PrimaryCapsule(nn.Module):\n", "    def __init__(self, in_channels, out_channels, dim_caps, kernel_size, stride=1, padding=0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.dim_caps = dim_caps\n", "        self.conv2d = nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding)\n", "\n", "    def forward(self, x):\n", "        outputs = self.conv2d(x)\n", "        # print(outputs.shape)\n", "        outputs = outputs.view(x.size(0), -1, self.dim_caps)\n", "        return squash(outputs)"]}, {"cell_type": "code", "execution_count": 266, "metadata": {}, "outputs": [], "source": ["class CapsuleNet2(nn.Module):\n", "    def __init__(self, input_size, classes, routings):\n", "        super(CapsuleNet2, self).__init__()\n", "        self.input_size = input_size\n", "        self.classes = classes\n", "        self.routings = routings\n", "\n", "        # Layer 1: Just a conventional Conv2D layer\n", "        self.conv1 = nn.Conv2d(3, 1, kernel_size=2, stride=1, padding=0)\n", "        \n", "        # Layer 2: Conv2D layer with `squash` activation, then reshape to [None, num_caps, dim_caps]\n", "        self.primarycaps = PrimaryCapsule(1, 1, 18, kernel_size=5, stride=2, padding=0)\n", "\n", "        # Layer 3: Capsule layer. Routing algorithm works here.\n", "        self.digitcaps = DenseCapsule(in_num_caps=93, in_dim_caps=18,\n", "                                      out_num_caps=classes, out_dim_caps=100, routings=routings)\n", "\n", "\n", "        self.relu = nn.ReLU()\n", "\n", "    def forward(self, x, y=None):\n", "        x = self.relu(self.conv1(x))\n", "        # print(\"x1.shape: \",x.shape) #[128, 64, 48, 98])  64和第一层有关 #[32, 1, 57, 127])\n", "        x = self.primarycaps(x) #torch.Size([128, 60, 8])\n", "        # print(\"x2.shape: \",x.shape) #\n", "        x = self.digitcaps(x) #[128, 16, 5])\n", "        # print(\"x3.shape: \",x.shape)\n", "        return x\n", "\n", "def caps_loss(y_true, y_pred, x, x_recon, lam_recon):\n", "    L = y_true * torch.clamp(0.9 - y_pred, min=0.) ** 2 + \\\n", "        0.5 * (1 - y_true) * torch.clamp(y_pred - 0.1, min=0.) ** 2\n", "    L_margin = L.sum(dim=1).mean()\n", "\n", "    L_recon = nn.MSELoss()(x_recon, x)\n", "\n", "    return L_margin + lam_recon * L_recon\n", "\n", "class capnet2(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in [3,3,3]\n", "                                    ])   \n", "        \n", "        self.cap = CapsuleNet2(([ 3, 58, 128]), classes=6, routings=3)\n", "        \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]                         # torch.<PERSON><PERSON>([32, 128, 58])    # torch.<PERSON><PERSON>([32, 128, 57])    # torch.Size([32, 128, 56])\n", "#         \n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        tacked_tensor = torch.stack([conved[0], conved[1], conved[2]], dim=3)\n", "        tacked_tensor = tacked_tensor.permute(0, 3, 2, 1) # [batch size, dim, sent len, embed_dim]   ([32, 3, 58, 128])\n", "        tacked_tensor = self.cap(tacked_tensor)           #[32, 6, 100]\n", "        tacked_tensor = F.max_pool1d(tacked_tensor, tacked_tensor.shape[2]).squeeze()\n", "        # print(tacked_tensor.shape)\n", "        return tacked_tensor\n"]}, {"cell_type": "code", "execution_count": 279, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import Embedding\n", "import torch.nn.functional as F\n", "\n", "class CoAttention(nn.Module):\n", "    def __init__(self,hidde_size:int,attention_size:int): #hidden_size:d, attention_size:k\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.hidden_size=hidde_size\n", "        self.Wl=nn.Parameter(torch.zeros(size=(hidde_size*2,hidde_size*2)),requires_grad=True)\n", "        self.Ws=nn.Parameter(torch.zeros(size=(attention_size,hidde_size*2)),requires_grad=True)\n", "        self.Wc=nn.Parameter(torch.zeros(size=(attention_size,hidde_size*2)),requires_grad=True)\n", "        self.whs=nn.Parameter(torch.zeros(size=(1,attention_size)),requires_grad=True)\n", "        self.whc=nn.Parameter(torch.zeros(size=(1,attention_size)),requires_grad=True)\n", "        self.reset_parameters()\n", "        \n", "    def reset_parameters(self):\n", "        self.Wl.data.uniform_(-1.0,1.0)\n", "        self.Ws.data.uniform_(-1.0,1.0)\n", "        self.Wc.data.uniform_(-1.0,1.0)\n", "        self.whs.data.uniform_(-1.0,1.0)\n", "        self.whc.data.uniform_(-1.0,1.0)\n", "\n", "    def forward(self,new_batch,entity_desc_batch):\n", "        # news_batch: [batch size, N, hidden size *2] hidden size:h\n", "        S=torch.transpose(new_batch,1,2)\n", "        # entity_desc_batch: [batch size, T, hidden size *2] T: entity description sentences for a news\n", "        C=torch.transpose(entity_desc_batch,1,2)\n", "\n", "        attF=torch.tanh(torch.bmm(torch.transpose(C,1,2),torch.matmul(self.Wl,S))) #dim [batch_size,T,N]\n", "\n", "        WsS=torch.matmul(self.Ws,S) #dim[batch,a,N] a:attention size\n", "        WsC=torch.mat<PERSON>l(self.Wc,<PERSON>) #dim[batch,a,T]\n", "\n", "        Hs=torch.tanh(WsS+torch.bmm(WsC,attF)) #dim[batch,a,N]\n", "        Hc=torch.tanh(WsC+torch.bmm(WsS,torch.transpose(attF,1,2))) #dim[batch,a,T]\n", "\n", "\n", "        a_s=<PERSON>.softmax(torch.matmul(self.whs,Hs),dim=2) #dim[batch,1,N]\n", "        a_c=<PERSON>.softmax(torch.matmul(self.whc,Hc),dim=2) #dim[batch,1,T]\n", "\n", "        s=torch.bmm(a_s,new_batch) # dim[batch,1,2h]\n", "        c=torch.bmm(a_c,entity_desc_batch)#[batch,1,2h]\n", "        return s,c,a_s,a_c\n", "    \n", "class Classifier(nn.Mo<PERSON>le):\n", "    def __init__(self,hidden_size:int):\n", "        super(Classifier, self).__init__()\n", "        # self.l1=nn.Linear(hidden_size*,hidden_size*4) # news sent+kb sent concatenation\n", "        self.l2=nn.Linear(hidden_size*4,6) \n", "\n", "    def forward(self,news_batch):\n", "        # layer1=self.l1(news_batch)\n", "        ans=self.l2(news_batch)\n", "        y=torch.squeeze(ans,1)\n", "        #y=<PERSON>.softmax(ans,dim=1)\n", "        return y\n", "\n", "class TextTextual(nn.Mo<PERSON>le):\n", "    def __init__(self, vocab_size, embedding_dim, input_dim,n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "        hidden_size = 64\n", "        num_layers = 1\n", "        self.hidden_dim = 64\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.embedding1 = nn.Embedding(vocab_size, input_dim, padding_idx = pad_idx)\n", "        self.gru=nn.LSTM(input_size=embedding_dim,hidden_size=hidden_size,num_layers=num_layers,batch_first=True,dropout=dropout,bidirectional=True)\n", "        self.gru1=nn.LSTM(input_size=input_dim,hidden_size=hidden_size,num_layers=num_layers,batch_first=True,dropout=dropout,bidirectional=True)\n", "        self.att = CoAttention(self.hidden_dim,attention_size=128)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.classifier=Classifier(self.hidden_dim)\n", "\n", "            \n", "    \n", "    def forward(self, text, textual):\n", "        #text = [batch size, sent len]\n", "        text_embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "        tu_embedded = self.embedding1(textual)   #torch.Size([32, 512, 128]) torch.Size([32, 60, 60])\n", "        text_embedded,_ = self.gru(text_embedded)\n", "        tu_embedded,_ = self.gru1(tu_embedded)\n", "        # print(text_embedded.shape, tu_embedded.shape)\n", "        contents_att, desc_att, contents_cd_att_weight,desc_att_weight = self.att(text_embedded, tu_embedded)\n", "        content_desc=torch.cat((contents_att,desc_att),2)\n", "        content_desc=F.softmax(content_desc,dim=2) #dim[batch,1,4h]\n", "        y=self.classifier(content_desc)\n", "\n", "\n", "        return y\n"]}, {"cell_type": "code", "execution_count": 280, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer1(nn.<PERSON><PERSON><PERSON>):  #广义钟形隶属函数\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>1, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_a = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_b = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_a)\n", "        nn.init.ones_(self.membership_b)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_a_exp = self.membership_a.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_b_exp = self.membership_b.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        # fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        fuzzy_membership = torch.mean(1/1+(torch.abs((input_seq_exp - membership_miu_exp)/membership_a_exp)) ** membership_b_exp, dim=-1)\n", "        return fuzzy_membership\n", "\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):   #高斯\n", "\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "#         input_dim: feature number of the dataset\n", "#         membership_num: number of membership function, also known as the class number\n", "        \n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "        \n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        \n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "            \n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "#         print(\"embedded:\",embedded.shape)\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "#       \n", "        \n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved1 = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved1, (metadata.size(0), 32))\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "    \n", "class fuzzyCo(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "        self.embedding = nn.Linear(input_dim_metadata, embedding_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.embedding1 = nn.Embedding(vocab_size, embedding_dim, padding_idx = padding_idx)\n", "        self.att = CoAttention(64,attention_size=64)\n", "        self.classifier=Classifier(64)\n", "    def forward(self, metadata_number,metadata_text):\n", "        textual_embedded = self.dropout(self.embedding(metadata_number))\n", "        num_embedded = self.embedding1(metadata_text)\n", "        textual_embedded = torch.reshape(textual_embedded, (metadata_number.size(0), 1,128))\n", "        contents_att, desc_att, contents_cd_att_weight,desc_att_weight = self.att(num_embedded, textual_embedded)\n", "        content_desc=torch.cat((contents_att,desc_att),2)\n", "        content_desc=F.softmax(content_desc,dim=2)\n", "        y=self.classifier(content_desc)\n", "        return y\n", "\n", "\n", "\n", "\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 3, output_dim)\n", "        # self.capNet1 = capnet1(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.capNet2 = capnet2(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.fuzzy1 = FuzzyLayer1(output_dim, output_dim)\n", "        self.TextTextual= TextTextual(vocab_size, embedding_dim,input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.fuzzyCo = fuzzyCo( vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional)\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        \n", "        # metadata_output_text = self.textcnn2(metadata_text)  #metadata_data[32,60]  metadata_output_text = torch.Size([32, 6])\n", "        # text_output = self.capNet1(text)\n", "        # text_output = self.textcnn(text)\n", "        # metadata_output_text = self.capNet2(metadata_text)\n", "        text_textual_output = self.TextTextual(text, metadata_text)\n", "\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        metadata_fuzzy_co = self.fuzzyCo(metadata_number,metadata_text)\n", "\n", "        fused_output = self.fuse(torch.cat((text_textual_output,metadata_fuzzy_co,metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 281, "metadata": {}, "outputs": [], "source": ["num_epochs =10  #3,5,7,10,15,20"]}, {"cell_type": "code", "execution_count": 282, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------- 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4424 *****\n", "Epoch [1/3], Time: 5.20s, Train Loss: 0.4926, Train Acc: 0.1737, Train F1 Macro: 0.1415, Train F1 Micro: 0.1737, Val Loss: 0.4424, Val Acc: 0.2695, Val F1 Macro: 0.1385, Val F1 Micro: 0.2695\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4318 *****\n", "Epoch [2/3], Time: 4.91s, Train Loss: 0.4361, Train Acc: 0.2972, Train F1 Macro: 0.1762, Train F1 Micro: 0.2354, Val Loss: 0.4318, Val Acc: 0.3092, Val F1 Macro: 0.1668, Val F1 Micro: 0.2893\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4190 *****\n", "Epoch [3/3], Time: 4.96s, Train Loss: 0.4227, Train Acc: 0.3445, Train F1 Macro: 0.1990, Train F1 Micro: 0.2718, Val Loss: 0.4190, Val Acc: 0.3544, Val F1 Macro: 0.1935, Val F1 Micro: 0.3110\n", "Total Training Time: 15.08s\n", "Test Loss: 0.4170, Test Acc: 0.3544, Test F1 Macro: 0.2404, Test F1 Micro: 0.3544\n", "----------------------------- 5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4409 *****\n", "Epoch [1/5], Time: 4.83s, Train Loss: 0.4844, Train Acc: 0.2287, Train F1 Macro: 0.1128, Train F1 Micro: 0.2287, Val Loss: 0.4409, Val Acc: 0.2780, Val F1 Macro: 0.1408, Val F1 Micro: 0.2780\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4306 *****\n", "Epoch [2/5], Time: 4.51s, Train Loss: 0.4354, Train Acc: 0.2990, Train F1 Macro: 0.1476, Train F1 Micro: 0.2639, <PERSON> Loss: 0.4306, Val Acc: 0.3162, <PERSON> F1 Macro: 0.1794, Val F1 Micro: 0.2971\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4188 *****\n", "Epoch [3/5], Time: 4.41s, Train Loss: 0.4240, Train Acc: 0.3523, Train F1 Macro: 0.1906, Train F1 Micro: 0.2934, Val Loss: 0.4188, Val Acc: 0.3941, Val F1 Macro: 0.2304, Val F1 Micro: 0.3294\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.4074 *****\n", "Epoch [4/5], Time: 4.45s, Train Loss: 0.4118, Train Acc: 0.3914, Train F1 Macro: 0.2230, Train F1 Micro: 0.3179, Val Loss: 0.4074, Val Acc: 0.3980, Val F1 Macro: 0.2518, Val F1 Micro: 0.3466\n", "***** Best Result Updated at Epoch 5, Val Loss: 0.3969 *****\n", "Epoch [5/5], Time: 4.49s, Train Loss: 0.4012, Train Acc: 0.4076, Train F1 Macro: 0.2491, Train F1 Micro: 0.3358, Val Loss: 0.3969, Val Acc: 0.4245, Val F1 Macro: 0.2820, Val F1 Micro: 0.3621\n", "Total Training Time: 22.68s\n", "Test Loss: 0.3991, Test Acc: 0.3954, Test F1 Macro: 0.3399, Test F1 Micro: 0.3954\n", "----------------------------- 7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4428 *****\n", "Epoch [1/7], Time: 4.86s, Train Loss: 0.4941, Train Acc: 0.2022, Train F1 Macro: 0.1098, Train F1 Micro: 0.2022, Val Loss: 0.4428, Val Acc: 0.2710, Val F1 Macro: 0.1622, Val F1 Micro: 0.2710\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4370 *****\n", "Epoch [2/7], Time: 4.92s, Train Loss: 0.4387, Train Acc: 0.2743, Train F1 Macro: 0.1519, Train F1 Micro: 0.2383, Val Loss: 0.4370, Val Acc: 0.2928, Val F1 Macro: 0.1744, Val F1 Micro: 0.2819\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4251 *****\n", "Epoch [3/7], Time: 4.91s, Train Loss: 0.4296, Train Acc: 0.3460, Train F1 Macro: 0.1833, Train F1 Micro: 0.2742, Val Loss: 0.4251, Val Acc: 0.3980, Val F1 Macro: 0.2221, Val F1 Micro: 0.3206\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.4147 *****\n", "Epoch [4/7], Time: 4.91s, Train Loss: 0.4166, Train Acc: 0.3915, Train F1 Macro: 0.2196, Train F1 Micro: 0.3035, Val Loss: 0.4147, Val Acc: 0.4229, Val F1 Macro: 0.2617, Val F1 Micro: 0.3462\n", "***** Best Result Updated at Epoch 5, Val Loss: 0.4027 *****\n", "Epoch [5/7], Time: 4.93s, Train Loss: 0.4044, Train Acc: 0.4057, Train F1 Macro: 0.2468, Train F1 Micro: 0.3239, Val Loss: 0.4027, Val Acc: 0.4252, Val F1 Macro: 0.2862, Val F1 Micro: 0.3620\n", "***** Best Result Updated at Epoch 6, Val Loss: 0.3961 *****\n", "Epoch [6/7], Time: 4.91s, Train Loss: 0.3947, Train Acc: 0.4131, Train F1 Macro: 0.2675, Train F1 Micro: 0.3388, Val Loss: 0.3961, Val Acc: 0.4400, Val F1 Macro: 0.3090, Val F1 Micro: 0.3750\n", "***** Best Result Updated at Epoch 7, Val Loss: 0.3899 *****\n", "Epoch [7/7], Time: 4.75s, Train Loss: 0.3864, Train Acc: 0.4350, Train F1 Macro: 0.2887, Train F1 Micro: 0.3525, Val Loss: 0.3899, Val Acc: 0.4626, Val F1 Macro: 0.3328, Val F1 Micro: 0.3875\n", "Total Training Time: 34.19s\n", "Test Loss: 0.3898, Test Acc: 0.4404, Test F1 Macro: 0.4362, Test F1 Micro: 0.4404\n", "----------------------------- 10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4411 *****\n", "Epoch [1/10], Time: 4.39s, Train Loss: 0.4864, Train Acc: 0.1999, Train F1 Macro: 0.1384, Train F1 Micro: 0.1999, Val Loss: 0.4411, Val Acc: 0.2173, Val F1 Macro: 0.0955, Val F1 Micro: 0.2173\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4342 *****\n", "Epoch [2/10], Time: 4.37s, Train Loss: 0.4369, Train Acc: 0.2759, Train F1 Macro: 0.1748, Train F1 Micro: 0.2379, <PERSON> Loss: 0.4342, Val Acc: 0.3061, Val F1 Macro: 0.1691, Val F1 Micro: 0.2617\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4233 *****\n", "Epoch [3/10], Time: 4.43s, Train Loss: 0.4279, Train Acc: 0.3145, Train F1 Macro: 0.1977, Train F1 Micro: 0.2634, Val Loss: 0.4233, Val Acc: 0.3427, Val F1 Macro: 0.2003, Val F1 Micro: 0.2887\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.4153 *****\n", "Epoch [4/10], Time: 4.84s, Train Loss: 0.4168, Train Acc: 0.3670, Train F1 Macro: 0.2245, Train F1 Micro: 0.2893, Val Loss: 0.4153, Val Acc: 0.3676, Val F1 Macro: 0.2157, Val F1 Micro: 0.3084\n", "***** Best Result Updated at Epoch 5, Val Loss: 0.4077 *****\n", "Epoch [5/10], Time: 4.93s, Train Loss: 0.4077, Train Acc: 0.3903, Train F1 Macro: 0.2451, Train F1 Micro: 0.3095, Val Loss: 0.4077, Val Acc: 0.3949, Val F1 Macro: 0.2325, Val F1 Micro: 0.3257\n", "***** Best Result Updated at Epoch 6, Val Loss: 0.4005 *****\n", "Epoch [6/10], Time: 4.91s, Train Loss: 0.3990, Train Acc: 0.4082, Train F1 Macro: 0.2639, Train F1 Micro: 0.3260, Val Loss: 0.4005, Val Acc: 0.3988, Val F1 Macro: 0.2490, Val F1 Micro: 0.3379\n", "***** Best Result Updated at Epoch 7, Val Loss: 0.3939 *****\n", "Epoch [7/10], Time: 5.03s, Train Loss: 0.3922, Train Acc: 0.4199, Train F1 Macro: 0.2798, Train F1 Micro: 0.3394, Val Loss: 0.3939, Val Acc: 0.4190, Val F1 Macro: 0.2719, Val F1 Micro: 0.3495\n", "***** Best Result Updated at Epoch 8, Val Loss: 0.3852 *****\n", "Epoch [8/10], Time: 4.97s, Train Loss: 0.3853, Train Acc: 0.4350, Train F1 Macro: 0.2984, Train F1 Micro: 0.3513, Val Loss: 0.3852, Val Acc: 0.4447, Val F1 Macro: 0.2962, Val F1 Micro: 0.3614\n", "***** Best Result Updated at Epoch 9, Val Loss: 0.3799 *****\n", "Epoch [9/10], Time: 5.01s, Train Loss: 0.3789, Train Acc: 0.4411, Train F1 Macro: 0.3146, Train F1 Micro: 0.3613, Val Loss: 0.3799, Val Acc: 0.4385, Val F1 Macro: 0.3129, Val F1 Micro: 0.3699\n", "***** Best Result Updated at Epoch 10, Val Loss: 0.3748 *****\n", "Epoch [10/10], Time: 4.94s, Train Loss: 0.3741, Train Acc: 0.4387, Train F1 Macro: 0.3272, Train F1 Micro: 0.3690, Val Loss: 0.3748, Val Acc: 0.4276, Val F1 Macro: 0.3246, Val F1 Micro: 0.3757\n", "Total Training Time: 47.82s\n", "Test Loss: 0.3765, Test Acc: 0.4144, Test F1 Macro: 0.4024, Test F1 Micro: 0.4144\n", "----------------------------- 15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4418 *****\n", "Epoch [1/15], Time: 4.35s, Train Loss: 0.4722, Train Acc: 0.2160, Train F1 Macro: 0.1513, Train F1 Micro: 0.2160, Val Loss: 0.4418, Val Acc: 0.3217, Val F1 Macro: 0.2001, Val F1 Micro: 0.3217\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4293 *****\n", "Epoch [2/15], Time: 4.39s, Train Loss: 0.4356, Train Acc: 0.3345, Train F1 Macro: 0.1939, Train F1 Micro: 0.2752, Val Loss: 0.4293, Val Acc: 0.3575, Val F1 Macro: 0.2149, Val F1 Micro: 0.3396\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4166 *****\n", "Epoch [3/15], Time: 4.39s, Train Loss: 0.4233, Train Acc: 0.3563, Train F1 Macro: 0.2116, Train F1 Micro: 0.3022, Val Loss: 0.4166, Val Acc: 0.3847, Val F1 Macro: 0.2355, Val F1 Micro: 0.3546\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.4047 *****\n", "Epoch [4/15], Time: 4.74s, Train Loss: 0.4105, Train Acc: 0.3805, Train F1 Macro: 0.2304, Train F1 Micro: 0.3218, Val Loss: 0.4047, Val Acc: 0.4190, Val F1 Macro: 0.2661, Val F1 Micro: 0.3707\n", "***** Best Result Updated at Epoch 5, Val Loss: 0.3938 *****\n", "Epoch [5/15], Time: 4.89s, Train Loss: 0.3995, Train Acc: 0.3985, Train F1 Macro: 0.2481, Train F1 Micro: 0.3371, Val Loss: 0.3938, Val Acc: 0.4291, Val F1 Macro: 0.2915, Val F1 Micro: 0.3824\n", "***** Best Result Updated at Epoch 6, Val Loss: 0.3869 *****\n", "Epoch [6/15], Time: 4.88s, Train Loss: 0.3910, Train Acc: 0.4197, Train F1 Macro: 0.2695, Train F1 Micro: 0.3509, Val Loss: 0.3869, Val Acc: 0.4291, Val F1 Macro: 0.3105, Val F1 Micro: 0.3902\n", "***** Best Result Updated at Epoch 7, Val Loss: 0.3801 *****\n", "Epoch [7/15], Time: 4.97s, Train Loss: 0.3839, Train Acc: 0.4259, Train F1 Macro: 0.2860, Train F1 Micro: 0.3616, Val Loss: 0.3801, Val Acc: 0.4517, Val F1 Macro: 0.3292, Val F1 Micro: 0.3990\n", "***** Best Result Updated at Epoch 8, Val Loss: 0.3768 *****\n", "Epoch [8/15], Time: 4.91s, Train Loss: 0.3784, Train Acc: 0.4286, Train F1 Macro: 0.3008, Train F1 Micro: 0.3700, Val Loss: 0.3768, Val Acc: 0.4424, Val F1 Macro: 0.3433, Val F1 Micro: 0.4044\n", "***** Best Result Updated at Epoch 9, Val Loss: 0.3731 *****\n", "Epoch [9/15], Time: 5.02s, Train Loss: 0.3745, Train Acc: 0.4362, Train F1 Macro: 0.3134, Train F1 Micro: 0.3774, Val Loss: 0.3731, Val Acc: 0.4540, Val F1 Macro: 0.3543, Val F1 Micro: 0.4099\n", "***** Best Result Updated at Epoch 10, Val Loss: 0.3699 *****\n", "Epoch [10/15], Time: 5.02s, Train Loss: 0.3702, Train Acc: 0.4347, Train F1 Macro: 0.3243, Train F1 Micro: 0.3831, Val Loss: 0.3699, Val Acc: 0.4455, Val F1 Macro: 0.3613, Val F1 Micro: 0.4135\n", "***** Best Result Updated at Epoch 11, Val Loss: 0.3683 *****\n", "Epoch [11/15], Time: 4.53s, Train Loss: 0.3680, Train Acc: 0.4371, Train F1 Macro: 0.3328, Train F1 Micro: 0.3880, Val Loss: 0.3683, Val Acc: 0.4540, Val F1 Macro: 0.3698, Val F1 Micro: 0.4172\n", "***** Best Result Updated at Epoch 12, Val Loss: 0.3665 *****\n", "Epoch [12/15], Time: 4.53s, Train Loss: 0.3658, Train Acc: 0.4440, Train F1 Macro: 0.3418, Train F1 Micro: 0.3927, Val Loss: 0.3665, Val Acc: 0.4657, Val F1 Macro: 0.3778, Val F1 Micro: 0.4212\n", "***** Best Result Updated at Epoch 13, Val Loss: 0.3650 *****\n", "Epoch [13/15], Time: 4.54s, Train Loss: 0.3632, Train Acc: 0.4503, Train F1 Macro: 0.3505, Train F1 Micro: 0.3971, Val Loss: 0.3650, Val Acc: 0.4525, Val F1 Macro: 0.3835, Val F1 Micro: 0.4236\n", "***** Best Result Updated at Epoch 14, Val Loss: 0.3616 *****\n", "Epoch [14/15], Time: 4.75s, Train Loss: 0.3606, Train Acc: 0.4510, Train F1 Macro: 0.3580, Train F1 Micro: 0.4009, Val Loss: 0.3616, Val Acc: 0.4595, Val F1 Macro: 0.3890, Val F1 Micro: 0.4262\n", "***** Best Result Updated at Epoch 15, Val Loss: 0.3587 *****\n", "Epoch [15/15], Time: 5.08s, Train Loss: 0.3593, Train Acc: 0.4522, Train F1 Macro: 0.3644, Train F1 Micro: 0.4044, Val Loss: 0.3587, Val Acc: 0.4681, Val F1 Macro: 0.3942, Val F1 Micro: 0.4290\n", "Total Training Time: 70.99s\n", "Test Loss: 0.3669, Test Acc: 0.4357, Test F1 Macro: 0.4311, Test F1 Micro: 0.4357\n", "----------------------------- 20\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.4395 *****\n", "Epoch [1/20], Time: 4.84s, Train Loss: 0.4804, Train Acc: 0.1997, Train F1 Macro: 0.1142, Train F1 Micro: 0.1997, Val Loss: 0.4395, Val Acc: 0.2741, Val F1 Macro: 0.1303, Val F1 Micro: 0.2741\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.4286 *****\n", "Epoch [2/20], Time: 4.88s, Train Loss: 0.4338, Train Acc: 0.2799, Train F1 Macro: 0.1586, Train F1 Micro: 0.2398, Val Loss: 0.4286, Val Acc: 0.2765, Val F1 Macro: 0.1336, Val F1 Micro: 0.2753\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.4167 *****\n", "Epoch [3/20], Time: 4.87s, Train Loss: 0.4229, Train Acc: 0.2899, Train F1 Macro: 0.1804, Train F1 Micro: 0.2565, <PERSON> Loss: 0.4167, Val Acc: 0.2928, Val F1 Macro: 0.1359, Val F1 Micro: 0.2812\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.4086 *****\n", "Epoch [4/20], Time: 4.90s, Train Loss: 0.4145, Train Acc: 0.3218, Train F1 Macro: 0.2032, Train F1 Micro: 0.2728, Val Loss: 0.4086, Val Acc: 0.3442, Val F1 Macro: 0.1813, Val F1 Micro: 0.2969\n", "***** Best Result Updated at Epoch 5, Val Loss: 0.4035 *****\n", "Epoch [5/20], Time: 4.92s, Train Loss: 0.4066, Train Acc: 0.3502, Train F1 Macro: 0.2264, Train F1 Micro: 0.2883, Val Loss: 0.4035, Val Acc: 0.3388, Val F1 Macro: 0.2080, Val F1 Micro: 0.3053\n", "***** Best Result Updated at Epoch 6, Val Loss: 0.3974 *****\n", "Epoch [6/20], Time: 4.59s, Train Loss: 0.4012, Train Acc: 0.3672, Train F1 Macro: 0.2453, Train F1 Micro: 0.3014, Val Loss: 0.3974, Val Acc: 0.4011, Val F1 Macro: 0.2401, Val F1 Micro: 0.3213\n", "***** Best Result Updated at Epoch 7, Val Loss: 0.3890 *****\n", "Epoch [7/20], Time: 4.44s, Train Loss: 0.3952, Train Acc: 0.4124, Train F1 Macro: 0.2653, Train F1 Micro: 0.3173, Val Loss: 0.3890, Val Acc: 0.4400, Val F1 Macro: 0.2744, Val F1 Micro: 0.3382\n", "***** Best Result Updated at Epoch 8, Val Loss: 0.3846 *****\n", "Epoch [8/20], Time: 4.46s, Train Loss: 0.3889, Train Acc: 0.4320, Train F1 Macro: 0.2832, Train F1 Micro: 0.3316, Val Loss: 0.3846, Val Acc: 0.4369, Val F1 Macro: 0.2953, Val F1 Micro: 0.3506\n", "***** Best Result Updated at Epoch 9, Val Loss: 0.3786 *****\n", "Epoch [9/20], Time: 4.56s, Train Loss: 0.3827, Train Acc: 0.4323, Train F1 Macro: 0.2971, Train F1 Micro: 0.3428, Val Loss: 0.3786, Val Acc: 0.4377, Val F1 Macro: 0.3102, Val F1 Micro: 0.3602\n", "***** Best Result Updated at Epoch 10, Val Loss: 0.3723 *****\n", "Epoch [10/20], Time: 5.00s, Train Loss: 0.3781, Train Acc: 0.4334, Train F1 Macro: 0.3084, Train F1 Micro: 0.3519, Val Loss: 0.3723, Val Acc: 0.4525, Val F1 Macro: 0.3236, Val F1 Micro: 0.3695\n", "***** Best Result Updated at Epoch 11, Val Loss: 0.3692 *****\n", "Epoch [11/20], Time: 5.01s, Train Loss: 0.3731, Train Acc: 0.4353, Train F1 Macro: 0.3176, Train F1 Micro: 0.3595, Val Loss: 0.3692, Val Acc: 0.4431, Val F1 Macro: 0.3336, Val F1 Micro: 0.3762\n", "***** Best Result Updated at Epoch 12, Val Loss: 0.3680 *****\n", "Epoch [12/20], Time: 4.98s, Train Loss: 0.3696, Train Acc: 0.4366, Train F1 Macro: 0.3257, Train F1 Micro: 0.3659, Val Loss: 0.3680, Val Acc: 0.4463, Val F1 Macro: 0.3417, Val F1 Micro: 0.3820\n", "***** Best Result Updated at Epoch 13, Val Loss: 0.3659 *****\n", "Epoch [13/20], Time: 4.99s, Train Loss: 0.3668, Train Acc: 0.4341, Train F1 Macro: 0.3323, Train F1 Micro: 0.3711, Val Loss: 0.3659, Val Acc: 0.4385, Val F1 Macro: 0.3481, Val F1 Micro: 0.3864\n", "***** Best Result Updated at Epoch 14, Val Loss: 0.3646 *****\n", "Epoch [14/20], Time: 5.04s, Train Loss: 0.3638, Train Acc: 0.4377, Train F1 Macro: 0.3383, Train F1 Micro: 0.3759, Val Loss: 0.3646, Val Acc: 0.4361, Val F1 Macro: 0.3531, Val F1 Micro: 0.3899\n", "***** Best Result Updated at Epoch 15, Val Loss: 0.3610 *****\n", "Epoch [15/20], Time: 5.04s, Train Loss: 0.3604, Train Acc: 0.4414, Train F1 Macro: 0.3440, Train F1 Micro: 0.3803, Val Loss: 0.3610, Val Acc: 0.4486, Val F1 Macro: 0.3587, Val F1 Micro: 0.3938\n", "***** Best Result Updated at Epoch 16, Val Loss: 0.3588 *****\n", "Epoch [16/20], Time: 4.73s, Train Loss: 0.3593, Train Acc: 0.4422, Train F1 Macro: 0.3495, Train F1 Micro: 0.3841, Val Loss: 0.3588, Val Acc: 0.4548, Val F1 Macro: 0.3638, Val F1 Micro: 0.3976\n", "***** Best Result Updated at Epoch 17, Val Loss: 0.3580 *****\n", "Epoch [17/20], Time: 4.57s, Train Loss: 0.3576, Train Acc: 0.4505, Train F1 Macro: 0.3549, Train F1 Micro: 0.3880, Val Loss: 0.3580, Val Acc: 0.4603, Val F1 Macro: 0.3684, Val F1 Micro: 0.4013\n", "***** Best Result Updated at Epoch 18, Val Loss: 0.3566 *****\n", "Epoch [18/20], Time: 4.61s, Train Loss: 0.3552, Train Acc: 0.4448, Train F1 Macro: 0.3597, Train F1 Micro: 0.3912, Val Loss: 0.3566, Val Acc: 0.4533, Val F1 Macro: 0.3719, Val F1 Micro: 0.4042\n", "***** Best Result Updated at Epoch 19, Val Loss: 0.3550 *****\n", "Epoch [19/20], Time: 4.62s, Train Loss: 0.3533, Train Acc: 0.4561, Train F1 Macro: 0.3647, Train F1 Micro: 0.3946, Val Loss: 0.3550, Val Acc: 0.4455, Val F1 Macro: 0.3747, Val F1 Micro: 0.4064\n", "Epoch [20/20], Time: 5.03s, Train Loss: 0.3518, Train Acc: 0.4519, Train F1 Macro: 0.3689, Train F1 Micro: 0.3975, Val Loss: 0.3554, Val Acc: 0.4525, Val F1 Macro: 0.3782, Val F1 Micro: 0.4087\n", "Total Training Time: 96.10s\n", "Test Loss: 0.3603, Test Acc: 0.4238, Test F1 Macro: 0.3938, Test F1 Micro: 0.4238\n"]}], "source": ["for num_epochs in [3,5,7,10,15,20]:\n", "    print(\"-----------------------------\",num_epochs)\n", "    vocab_size = 30522\n", "    embedding_dim = 128\n", "    n_filters = 128\n", "    filter_sizes = [3,4,5]\n", "    output_dim = 6\n", "    dropout = 0.5\n", "    padding_idx = 0\n", "    input_dim = 6 * metadata_each_dim\n", "    input_dim_metadata = 5\n", "    hidden_dim = 64\n", "    n_layers = 1\n", "    bidirectional = True\n", "\n", "    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "    # Define the optimizer and loss function\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "    # Record the training process\n", "    Train_acc = []\n", "    Train_loss = []\n", "    Train_macro_f1 = []\n", "    Train_micro_f1 = []\n", "\n", "    Val_acc = []\n", "    Val_loss = []\n", "    Val_macro_f1 = []\n", "    Val_micro_f1 = []\n", "\n", "    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "        epoch_trained = 0\n", "        train_label_all = []\n", "        train_predict_all = []\n", "        val_label_all = []\n", "        val_predict_all = []\n", "        best_valid_loss = float('inf')\n", "\n", "        start_time = time.time()\n", "        for epoch in range(num_epochs):\n", "            epoch_trained += 1\n", "            epoch_start_time = time.time()\n", "            # Training\n", "            model.train()\n", "            train_loss = 0.0\n", "            train_accuracy = 0.0\n", "            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                optimizer.zero_grad()\n", "                outputs = model(statements, metadata_text, metadata_number)\n", "                loss = criterion(outputs, label_onehot)\n", "                loss.backward()\n", "                optimizer.step()\n", "\n", "                train_loss += loss.item()\n", "                _, train_predicted = torch.max(outputs, 1)\n", "                train_accuracy += sum(train_predicted == label)\n", "                train_predict_all += train_predicted.tolist()\n", "                train_label_all += label.tolist()\n", "            train_loss /= len(train_loader)\n", "            train_accuracy /= len(train_loader.dataset)\n", "            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "            Train_acc.append(train_accuracy.tolist())\n", "            Train_loss.append(train_loss)\n", "            Train_macro_f1.append(train_macro_f1)\n", "            Train_micro_f1.append(train_micro_f1)\n", "\n", "            # Validation\n", "            model.eval()\n", "            val_loss = 0.0\n", "            val_accuracy = 0.0\n", "            with torch.no_grad():\n", "                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                    statements = statements.to(DEVICE)\n", "                    label_onehot = label_onehot.to(DEVICE)\n", "                    label = label.to(DEVICE)\n", "                    metadata_text = metadata_text.to(DEVICE)\n", "                    metadata_number = metadata_number.to(DEVICE)\n", "\n", "                    val_outputs = model(statements, metadata_text, metadata_number)\n", "                    val_loss += criterion(val_outputs, label_onehot).item()\n", "                    _, val_predicted = torch.max(val_outputs, 1)\n", "                    val_accuracy += sum(val_predicted == label)\n", "                    val_predict_all += val_predicted.tolist()\n", "                    val_label_all += label.tolist()\n", "            val_loss /= len(val_loader)\n", "            val_accuracy /= len(val_loader.dataset)\n", "            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "            Val_acc.append(val_accuracy.tolist())\n", "            Val_loss.append(val_loss)\n", "            Val_macro_f1.append(val_macro_f1)\n", "            Val_micro_f1.append(val_micro_f1)\n", "\n", "            if val_loss < best_valid_loss:\n", "                best_valid_loss = val_loss\n", "                torch.save(model.state_dict(), model_save)\n", "                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "            # Print the losses and accuracy\n", "            epoch_end_time = time.time()\n", "            epoch_time = epoch_end_time - epoch_start_time\n", "\n", "            print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "        end_time = time.time()\n", "        training_time = end_time - start_time\n", "        print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "    # Evaluate the model on new data\n", "    def test(model, test_loader, model_save):\n", "        model.load_state_dict(torch.load(model_save))\n", "        model.eval()\n", "\n", "        test_label_all = []\n", "        test_predict_all = []\n", "\n", "        test_loss = 0.0\n", "        test_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                test_outputs = model(statements, metadata_text, metadata_number)\n", "                test_loss += criterion(test_outputs, label_onehot).item()\n", "                _, test_predicted = torch.max(test_outputs, 1)\n", "                \n", "                test_accuracy += sum(test_predicted == label)\n", "                test_predict_all += test_predicted.tolist()\n", "                test_label_all += label.tolist()\n", "\n", "        test_loss /= len(test_loader)\n", "        test_accuracy /= len(test_loader.dataset)\n", "        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "    test(model, test_loader, model_save)"]}, {"cell_type": "code", "execution_count": 271, "metadata": {}, "outputs": [], "source": ["# torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 272, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        return fuzzy_membership\n", "\n", "## senti batch_size = 128\n", "class SentAttNet(nn.Module):\n", "    def __init__(self,  hidden_size=50):\n", "        super(SentAttNet, self).__init__()\n", "        self.sent_weight = nn.Parameter(torch.Tensor(2 * hidden_size, 10))\n", "        self.sent_bias = nn.Parameter(torch.Tensor(10))\n", "        self.context_weight = nn.Parameter(torch.Tensor(10, 1))\n", "        self.gru1 = nn.GRU(510, hidden_size, bidirectional=True)  #将GRU改成LSTM\n", "        self.gru2 = nn.GRU(509, hidden_size, bidirectional=True)\n", "        self.gru3 = nn.GRU(508, hidden_size, bidirectional=True)\n", "        self.gru4 = nn.GRU(58, hidden_size, bidirectional=True)\n", "        self.gru5 = nn.GRU(57, hidden_size, bidirectional=True)\n", "        self.gru6 = nn.GRU(56, hidden_size, bidirectional=True)\n", "        self._create_weights(mean=0.0, std=0.05)\n", "        self.hidden_size = hidden_size\n", "    def _create_weights(self, mean=0.0, std=0.05):\n", "        self.sent_weight.data.normal_(mean, std)\n", "        self.context_weight.data.normal_(mean, std)\n", "\n", "    def forward(self, input, hidden_state):\n", "#         f_output, h_output = self.gru(input, hidden_state)\n", "        input.to('cuda')\n", "        hidden_state.to('cuda')\n", "        input_shape = input.size()\n", "        if(input_shape[-1]==510):\n", "            f_output, h_output = self.gru1(input, hidden_state)\n", "        elif(input_shape[-1]==509):\n", "            f_output, h_output = self.gru2(input, hidden_state)\n", "        elif(input_shape[-1]==508):\n", "            f_output, h_output = self.gru3(input, hidden_state)\n", "#         elif(input_shape[-1]==58):\n", "#             f_output, h_output = self.gru4(input, hidden_state)\n", "#         elif(input_shape[-1]==57):\n", "#             f_output, h_output = self.gru5(input, hidden_state)\n", "#         else:\n", "# #             print(input_shape)\n", "#             f_output, h_output = self.gru6(input, hidden_state)\n", "        output = matrix_mul(f_output, self.sent_weight, self.context_weight, self.sent_bias)\n", "        output = F.softmax(output)\n", "        output = element_wise_mul(output.permute(1,0,2), f_output.permute(1,0,2))\n", "\n", "        return output, h_output\n", "    \n", "def element_wise_mul(input1, input2):\n", "    feature_list = []\n", "    for feature_1, feature_2 in zip(input1, input2):\n", "        feature = (feature_1 * feature_2).sum(dim=0)\n", "        feature_list.append(feature)\n", "\n", "    output = torch.stack(feature_list, dim=0)\n", "    return output\n", "\n", "def matrix_mul(input, weight, context_weight,  bias=False):\n", "    input = torch.matmul(input, weight)\n", "    input = input + bias\n", "    input = torch.tanh(input)\n", "    input = torch.matmul(input, context_weight)\n", "\n", "    return input\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.batch_size = 128\n", "        self.sent_hidden_size = 50\n", "        self._init_hidden_state()\n", "        self.sent = SentAttNet( hidden_size=50)\n", "        \n", "    def _init_hidden_state(self, last_batch_size=None):\n", "        if last_batch_size:\n", "            batch_size = last_batch_size\n", "        else:\n", "            batch_size = self.batch_size\n", "        self.sent_hidden_state = torch.zeros(2, batch_size, self.sent_hidden_size)\n", "        if torch.cuda.is_available():\n", "            self.sent_hidden_state = self.sent_hidden_state.cuda()\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "#         print(\"embedded:\",embedded.shape)\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "#          因为conved是list，GRU因为时间步需要tensor\n", "#         for i in range(len(conved)):\n", "#             print(\"i:\",conved[i].shape)\n", "\n", "        # conv1 = []\n", "        # for conv in conved:\n", "        #     conv , self.sent_hidden_state= self.sent(conv , self.sent_hidden_state)\n", "        #     conv1.append(conv)\n", "        \n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "    \n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "TEXTGCN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}