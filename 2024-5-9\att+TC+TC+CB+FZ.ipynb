{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 实验较好 epoch=9"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 2.1.0+cu121\n", "cuda\n"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "worksapce = 'D:/code/HLAN/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "# Define the data loaders for training and validation\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MODEL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### capsnet"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from torch.autograd import Variable\n", "import math\n", "class ConvLayer(nn.Module):\n", "    \"\"\" N-gram convolutional layer\n", "    \n", "        Args:\n", "            in_channels: convolutional input channels\n", "            out_channels: convolutional output channels\n", "            kernel_size: convolutional kernel size\n", "            stride: convolutional stride\n", "    \n", "    \"\"\"\n", "    def __init__(self, in_channels, out_channels, kernel_size, stride):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.convnet = nn.Sequential(\n", "            nn.Conv1d(\n", "                in_channels=in_channels, \n", "                out_channels=out_channels, \n", "                kernel_size=kernel_size,\n", "                stride=stride\n", "            ),\n", "            nn.ELU(),\n", "        )\n", "\n", "    def forward(self, x):\n", "        return self.convnet(x)\n", "    \n", "def calculate_conv_output(input_, kernel, padding, stride):\n", "    \"\"\"Calculate the output size in Convolution layer\"\"\"\n", "    return math.floor(((input_ - kernel + 2 * padding) / stride) + 1)\n", "class PrimaryCaps(nn.Module):\n", "    \"\"\" Primary caps layer\n", "    \n", "    \n", "        Args:\n", "            num_capsules: capsules count\n", "            in_channels: input channels\n", "            out_channels: output channels\n", "            kernel_size: capsule kernel size\n", "            stride: capsule stride\n", "            conv_out_size: tensor size from above layer\n", "\n", "    \"\"\"\n", "    def __init__(self, num_capsules, in_channels, out_channels, kernel_size, stride, conv_out_size):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.out_channels = out_channels\n", "        self.capsules = nn.ModuleList([\n", "            nn.Conv1d(\n", "                in_channels=in_channels, \n", "                out_channels=out_channels, \n", "                kernel_size=kernel_size, \n", "                stride=stride, \n", "                padding=0\n", "            ) \n", "            for _ in range(num_capsules)\n", "        ])\n", "        \n", "        self._out_channels = out_channels\n", "        self._conv_out_size = conv_out_size\n", "    \n", "    def forward(self, x):\n", "        u = [capsule(x) for capsule in self.capsules]\n", "        u = torch.stack(u, dim=1)\n", "        u = u.view(x.size(0), self._out_channels * self._conv_out_size , -1)\n", "\n", "        return self.squash(u)\n", "    \n", "    def squash(self, input_tensor):\n", "        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)\n", "        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))\n", "\n", "        return output_tensor\n", "\n", "class SecondaryCaps(nn.Module):\n", "    \"\"\" Secondary capsules layer\n", "    \n", "        Args:\n", "            num_capsules: capsules count\n", "            num_routes: routing iteration count\n", "            in_channels: in channels dim\n", "            out_channels: out channels dim\n", "    \n", "    \"\"\"\n", "    def __init__(self, num_capsules, num_routes, in_channels, out_channels):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.in_channels = in_channels\n", "        self.num_routes = num_routes\n", "        self.num_capsules = num_capsules\n", "\n", "        self.W = nn.Parameter(torch.randn(1, num_routes, num_capsules, out_channels, in_channels))\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = torch.stack([x] * self.num_capsules, dim=2).unsqueeze(4)\n", "\n", "        W = torch.cat([self.W] * batch_size, dim=0)\n", "        print(x.shape, W.shape)\n", "        u_hat = torch.matmul(W, x)\n", "\n", "        b_ij = Variable(torch.zeros(1, self.num_routes, self.num_capsules, 1)).to(cuda)\n", "\n", "        num_iterations = 3\n", "        for iteration in range(num_iterations):\n", "            c_ij = <PERSON>.softmax(b_ij, dim=2)\n", "\n", "            c_ij = torch.cat([c_ij] * batch_size, dim=0).unsqueeze(4)\n", "\n", "            s_j = (c_ij * u_hat).sum(dim=1, keepdim=True)\n", "            v_j = self.squash(s_j)\n", "            \n", "            if iteration < num_iterations - 1:\n", "                a_ij = torch.matmul(u_hat.transpose(3, 4), torch.cat([v_j] * self.num_routes, dim=1))\n", "                b_ij = b_ij + a_ij.squeeze(4).mean(dim=0, keepdim=True)\n", "\n", "        return v_j.squeeze(1)\n", "    \n", "    def squash(self, input_tensor):\n", "        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)\n", "        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))\n", "\n", "        return output_tensor\n", "    \n", "class CapsNet(nn.Module):\n", "    \"\"\" Caps net module of CBAE\n", "    \n", "        Args:\n", "            conv_out_ch: N-gram convolutional out channels\n", "            conv_kernel: N-gram convolutional kernel size\n", "            conv_stride: N-gram convolutional stride\n", "            prime_num_capsules: Primary capsules count\n", "            prime_out_ch: Primary capsules out channels\n", "            prime_kernel: Primary capsules kernel size\n", "            prime_stride: Primary capsules stride\n", "            secondary_num_capsules: Secondary capsules count\n", "            secondary_out_channels=Secondary capsules out channels\n", "            maxlen: sentence max length taken into account\n", "    \n", "    \"\"\"\n", "    def __init__(\n", "        self,\n", "        conv_in_ch,\n", "        conv_out_ch,\n", "        conv_kernel,\n", "        conv_stride,\n", "        prime_num_capsules,\n", "        prime_out_ch,\n", "        prime_kernel,\n", "        prime_stride,\n", "        secondary_num_capsules,\n", "        secondary_out_channels,\n", "        input_len,\n", "    ):\n", "        super(CapsNet, self).__init__()\n", "        self._secondary_out_size=secondary_out_channels * secondary_num_capsules\n", "        \n", "        self.conv_layer = ConvLayer(\n", "            in_channels=conv_in_ch,\n", "            out_channels=conv_out_ch,\n", "            kernel_size=conv_kernel,\n", "            stride=conv_stride,\n", "        )\n", "        conv_layer_output = calculate_conv_output(\n", "            input_=input_len, \n", "            kernel=conv_kernel, \n", "            padding=0, \n", "            stride=conv_stride,\n", "        )\n", "        \n", "        prime_caps_conv_output = calculate_conv_output(\n", "            input_=conv_layer_output, \n", "            kernel=prime_kernel, \n", "            padding=0, \n", "            stride=prime_stride,\n", "        )\n", "        \n", "        self.primary_caps = PrimaryCaps(\n", "            num_capsules=prime_num_capsules, \n", "            in_channels=conv_out_ch, \n", "            out_channels=prime_out_ch, \n", "            kernel_size=prime_kernel, \n", "            stride=prime_stride,\n", "            conv_out_size=prime_caps_conv_output,\n", "        )\n", "        \n", "        self.secondary_caps = SecondaryCaps(\n", "            num_capsules=secondary_num_capsules,\n", "            num_routes=prime_caps_conv_output * prime_out_ch,\n", "            in_channels=prime_num_capsules,\n", "            out_channels=secondary_out_channels,\n", "        )\n", "\n", "        self.fc = nn.Linear(secondary_out_channels * secondary_num_capsules, input_len)\n", "        self.tanh = nn.Tanh()\n", "        self.capsule_softmax = torch.nn.Softmax(dim=1)\n", "\n", "    def forward(self, data):\n", "        output = self.secondary_caps(self.primary_caps(self.conv_layer(data)))\n", "        output = output.reshape(-1, self._secondary_out_size)\n", "        output = F.relu(output)\n", "        output = self.fc(output)\n", "        return self.capsule_softmax(self.tanh(output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### attention"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from torch.nn.parameter import Parameter\n", "from torch.nn import init\n", "class SelfAttention(torch.nn.<PERSON>):\n", "    \"\"\"Self attention\n", "    \n", "    Args:\n", "        wv_dim: word vector sizeluence\n", "        maxlen: sentence max length taken into account\n", "    \n", "    \"\"\"\n", "    def __init__(self, wv_dim, maxlen):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.wv_dim = wv_dim\n", "\n", "        # max sentence length -- batch 2nd dim size\n", "        self.maxlen = maxlen\n", "        self.M = Parameter(torch.empty(size=(wv_dim, wv_dim)))\n", "        init.kaiming_uniform_(self.M.data)\n", "\n", "        self.tanh = nn.Tanh()\n", "        self.attention_softmax = torch.nn.Softmax(dim=1)\n", "\n", "    def forward(self, input_embeddings):\n", "        # (b, wv, 1)\n", "        mean_embedding = torch.mean(input_embeddings, (1,)).unsqueeze(2)\n", "\n", "        # (wv, wv) x (b, wv, 1) -> (b, wv, 1)\n", "        product_1 = torch.matmul(self.M, mean_embedding)\n", "\n", "        # (b, maxlen, wv) x (b, wv, 1) -> (b, maxlen, 1)\n", "        product_2 = torch.matmul(input_embeddings, product_1).squeeze(2)\n", "\n", "        results = self.attention_softmax(self.tanh(product_2))\n", "\n", "        return results\n", "\n", "    def extra_repr(self):\n", "        return 'wv_dim={}, maxlen={}'.format(self.wv_dim, self.maxlen)\n", "    \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### primary"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "class TextCap(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.caps_net = CapsNet(\n", "            conv_in_ch=128,\n", "            conv_out_ch=32,\n", "            conv_kernel=3,\n", "            conv_stride=1,\n", "            prime_num_capsules=7,\n", "            prime_out_ch=1,\n", "            prime_kernel=3,\n", "            prime_stride=1,\n", "            secondary_num_capsules=7,\n", "            secondary_out_channels=32,\n", "            input_len=32,\n", "        )\n", "    def get_aspects_importances(self, text_embeddings):\n", "        \"\"\"Get aspect importances\n", "        \n", "        Args:\n", "            text_embedding: embeddings of a sentence as input\n", "        \n", "        Returns: \n", "            capsule weights, aspects_importances, weighted_text_emb\n", "\n", "        \"\"\"\n", "        # compute capsule scores, looking at text embeddings average\n", "        caps_weights = self.caps_net(text_embeddings.permute(0, 2, 1))\n", "\n", "        # multiplying text embeddings by attention scores -- and summing\n", "        # (matmul: we sum every word embedding's coordinate with attention weights)\n", "        weighted_text_emb = torch.matmul(caps_weights.unsqueeze(1),  # (batch, 1, sentence)\n", "                                         text_embeddings  # (batch, sentence, wv_dim)\n", "                                         ).squeeze()\n", "\n", "        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)\n", "        raw_importances = self.linear_transform(weighted_text_emb)\n", "\n", "        # computing 'aspects distribution in a sentence'\n", "        aspects_importances = self.softmax_aspects(raw_importances)\n", "\n", "        return caps_weights, aspects_importances, weighted_text_emb\n", "\n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "        \n", "        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(embedded)\n", "        \n", "        print(aspects_importances.shape)\n", "        return aspects_importances\n", "\n", "class Attention(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.attention = SelfAttention(embedding_dim, -1)\n", "        self.linear_transform = torch.nn.Linear(embedding_dim, 6)\n", "        self.softmax_aspects = torch.nn.Softmax(dim=1)\n", "        self.aspects_embeddings = Parameter(torch.empty(size=(embedding_dim, 6)))\n", "    def get_aspects_importances(self, text_embeddings):\n", "        \"\"\"Get aspect importances\n", "        \n", "        Args:\n", "            text_embedding: embeddings of a sentence as input\n", "        \n", "        Returns: \n", "            attention weights, aspects_importances, weighted_text_emb\n", "\n", "        \"\"\"\n", "        # compute attention scores, looking at text embeddings average\n", "        attention_weights = self.attention(text_embeddings)\n", "\n", "        # multiplying text embeddings by attention scores -- and summing\n", "        # (matmul: we sum every word embedding's coordinate with attention weights)\n", "        weighted_text_emb = torch.matmul(attention_weights.unsqueeze(1),  # (batch, 1, sentence)\n", "                                         text_embeddings  # (batch, sentence, wv_dim)\n", "                                         ).squeeze()\n", "\n", "        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)\n", "        raw_importances = self.linear_transform(weighted_text_emb)\n", "\n", "        # computing 'aspects distribution in a sentence'\n", "        aspects_importances = self.softmax_aspects(raw_importances)\n", "\n", "        return attention_weights, aspects_importances, weighted_text_emb\n", "\n", "    def forward(self, text_embeddings):\n", "        \n", "        text_embeddings = self.embedding(text_embeddings)\n", "\n", "        # encoding: words embeddings -> sentence embedding, aspects importances\n", "        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(text_embeddings)\n", "        # print(aspects_importances.shape)\n", "        return aspects_importances\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 5, output_dim)\n", "\n", "        self.textcap = TextCap(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.attention = Attention(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.attention2 = Attention(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "        \n", "        attention_output = self.attention(text)\n", "        # metadata_attention_output = self.attention2(metadata_text)\n", "        fused_output = self.fuse(torch.cat((text_output,attention_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RUN"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------------ 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3736 *****\n", "Epoch [1/3], Time: 5.07s, Train Loss: 0.4286, Train Acc: 0.2991, Train F1 Macro: 0.2708, Train F1 Micro: 0.2991, Val Loss: 0.3736, Val Acc: 0.4424, Val F1 Macro: 0.4095, Val F1 Micro: 0.4424\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3537 *****\n", "Epoch [2/3], Time: 4.42s, Train Loss: 0.3660, Train Acc: 0.4519, Train F1 Macro: 0.3564, Train F1 Micro: 0.3755, Val Loss: 0.3537, Val Acc: 0.4790, Val F1 Macro: 0.4387, Val F1 Micro: 0.4607\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3472 *****\n", "Epoch [3/3], Time: 4.37s, Train Loss: 0.3404, Train Acc: 0.4995, Train F1 Macro: 0.4010, Train F1 Micro: 0.4168, Val Loss: 0.3472, Val Acc: 0.4696, Val F1 Macro: 0.4476, Val F1 Micro: 0.4637\n", "Total Training Time: 13.86s\n", "Test Loss: 0.3615, Test Acc: 0.4412, Test F1 Macro: 0.4386, Test F1 Micro: 0.4412\n", "------------ 5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3850 *****\n", "Epoch [1/5], Time: 4.32s, Train Loss: 0.4347, Train Acc: 0.2732, Train F1 Macro: 0.2375, Train F1 Micro: 0.2732, Val Loss: 0.3850, Val Acc: 0.4159, Val F1 Macro: 0.3602, Val F1 Micro: 0.4159\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3551 *****\n", "Epoch [2/5], Time: 4.32s, Train Loss: 0.3724, Train Acc: 0.4362, Train F1 Macro: 0.3211, Train F1 Micro: 0.3547, Val Loss: 0.3551, Val Acc: 0.4774, Val F1 Macro: 0.4143, Val F1 Micro: 0.4467\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3518 *****\n", "Epoch [3/5], Time: 4.22s, Train Loss: 0.3423, Train Acc: 0.4964, Train F1 Macro: 0.3759, Train F1 Micro: 0.4020, Val Loss: 0.3518, Val Acc: 0.4665, Val F1 Macro: 0.4345, Val F1 Micro: 0.4533\n", "Epoch [4/5], Time: 4.07s, Train Loss: 0.3188, Train Acc: 0.5402, Train F1 Macro: 0.4172, Train F1 Micro: 0.4365, Val Loss: 0.3545, Val Acc: 0.4727, Val F1 Macro: 0.4454, Val F1 Micro: 0.4581\n", "Epoch [5/5], Time: 4.10s, Train Loss: 0.2922, Train Acc: 0.5981, Train F1 Macro: 0.4535, Train F1 Micro: 0.4688, Val Loss: 0.3539, Val Acc: 0.4595, Val F1 Macro: 0.4475, Val F1 Micro: 0.4584\n", "Total Training Time: 21.02s\n", "Test Loss: 0.3582, Test Acc: 0.4278, Test F1 Macro: 0.4370, Test F1 Micro: 0.4278\n", "------------ 7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3842 *****\n", "Epoch [1/7], Time: 4.08s, Train Loss: 0.4270, Train Acc: 0.2994, Train F1 Macro: 0.2530, Train F1 Micro: 0.2994, Val Loss: 0.3842, Val Acc: 0.4050, Val F1 Macro: 0.3804, Val F1 Micro: 0.4050\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3553 *****\n", "Epoch [2/7], Time: 4.16s, Train Loss: 0.3709, Train Acc: 0.4398, Train F1 Macro: 0.3453, Train F1 Micro: 0.3696, Val Loss: 0.3553, Val Acc: 0.4595, Val F1 Macro: 0.4214, Val F1 Micro: 0.4322\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3469 *****\n", "Epoch [3/7], Time: 4.22s, Train Loss: 0.3417, Train Acc: 0.4919, Train F1 Macro: 0.3963, Train F1 Micro: 0.4104, Val Loss: 0.3469, Val Acc: 0.4463, Val F1 Macro: 0.4296, Val F1 Micro: 0.4369\n", "Epoch [4/7], Time: 4.26s, Train Loss: 0.3184, Train Acc: 0.5327, Train F1 Macro: 0.4321, Train F1 Micro: 0.4410, Val Loss: 0.3478, Val Acc: 0.4564, Val F1 Macro: 0.4380, Val F1 Micro: 0.4418\n", "Epoch [5/7], Time: 4.45s, Train Loss: 0.2919, Train Acc: 0.5859, Train F1 Macro: 0.4651, Train F1 Micro: 0.4700, Val Loss: 0.3477, Val Acc: 0.4595, Val F1 Macro: 0.4414, Val F1 Micro: 0.4453\n", "Epoch [6/7], Time: 4.53s, Train Loss: 0.2644, Train Acc: 0.6496, Train F1 Macro: 0.4975, Train F1 Micro: 0.4999, Val Loss: 0.3561, Val Acc: 0.4603, Val F1 Macro: 0.4444, Val F1 Micro: 0.4478\n", "Epoch [7/7], Time: 4.29s, Train Loss: 0.2315, Train Acc: 0.7180, Train F1 Macro: 0.5308, Train F1 Micro: 0.5311, Val Loss: 0.3729, Val Acc: 0.4502, Val F1 Macro: 0.4450, Val F1 Micro: 0.4482\n", "Total Training Time: 30.00s\n", "Test Loss: 0.3532, Test Acc: 0.4499, Test F1 Macro: 0.4478, Test F1 Micro: 0.4499\n", "------------ 9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3824 *****\n", "Epoch [1/9], Time: 4.16s, Train Loss: 0.4310, Train Acc: 0.2834, Train F1 Macro: 0.2526, Train F1 Micro: 0.2834, Val Loss: 0.3824, Val Acc: 0.4486, Val F1 Macro: 0.4130, Val F1 Micro: 0.4486\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3525 *****\n", "Epoch [2/9], Time: 4.13s, Train Loss: 0.3658, Train Acc: 0.4605, Train F1 Macro: 0.3557, Train F1 Micro: 0.3720, Val Loss: 0.3525, Val Acc: 0.4751, Val F1 Macro: 0.4404, Val F1 Micro: 0.4618\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3467 *****\n", "Epoch [3/9], Time: 4.21s, Train Loss: 0.3376, Train Acc: 0.4950, Train F1 Macro: 0.4020, Train F1 Micro: 0.4130, Val Loss: 0.3467, Val Acc: 0.4681, Val F1 Macro: 0.4534, Val F1 Micro: 0.4639\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.3418 *****\n", "Epoch [4/9], Time: 4.32s, Train Loss: 0.3177, Train Acc: 0.5348, Train F1 Macro: 0.4352, Train F1 Micro: 0.4434, Val Loss: 0.3418, Val Acc: 0.4805, Val F1 Macro: 0.4591, Val F1 Micro: 0.4681\n", "Epoch [5/9], Time: 4.17s, Train Loss: 0.2929, Train Acc: 0.5929, Train F1 Macro: 0.4678, Train F1 Micro: 0.4733, Val Loss: 0.3504, Val Acc: 0.4735, Val F1 Macro: 0.4612, Val F1 Micro: 0.4692\n", "Epoch [6/9], Time: 4.10s, Train Loss: 0.2606, Train Acc: 0.6564, Train F1 Macro: 0.5002, Train F1 Micro: 0.5038, Val Loss: 0.3676, Val Acc: 0.4572, Val F1 Macro: 0.4596, Val F1 Micro: 0.4672\n", "Epoch [7/9], Time: 4.17s, Train Loss: 0.2291, Train Acc: 0.7217, Train F1 Macro: 0.5328, Train F1 Micro: 0.5350, Val Loss: 0.3785, Val Acc: 0.4478, Val F1 Macro: 0.4577, Val F1 Micro: 0.4644\n", "Epoch [8/9], Time: 4.15s, Train Loss: 0.1902, Train Acc: 0.7949, Train F1 Macro: 0.5668, Train F1 Micro: 0.5675, Val Loss: 0.4137, Val Acc: 0.4447, Val F1 Macro: 0.4572, Val F1 Micro: 0.4619\n", "Epoch [9/9], Time: 4.13s, Train Loss: 0.1606, Train Acc: 0.8449, Train F1 Macro: 0.5984, Train F1 Micro: 0.5983, Val Loss: 0.4322, Val Acc: 0.4665, Val F1 Macro: 0.4581, Val F1 Micro: 0.4624\n", "Total Training Time: 37.53s\n", "Test Loss: 0.3471, Test Acc: 0.4704, Test F1 Macro: 0.4636, Test F1 Micro: 0.4704\n", "------------ 12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3837 *****\n", "Epoch [1/12], Time: 4.13s, Train Loss: 0.4329, Train Acc: 0.2853, Train F1 Macro: 0.2560, Train F1 Micro: 0.2853, Val Loss: 0.3837, Val Acc: 0.4283, Val F1 Macro: 0.3878, Val F1 Micro: 0.4283\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3577 *****\n", "Epoch [2/12], Time: 4.32s, Train Loss: 0.3694, Train Acc: 0.4512, Train F1 Macro: 0.3480, Train F1 Micro: 0.3682, Val Loss: 0.3577, Val Acc: 0.4525, Val F1 Macro: 0.4167, Val F1 Micro: 0.4404\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3536 *****\n", "Epoch [3/12], Time: 4.38s, Train Loss: 0.3406, Train Acc: 0.5014, Train F1 Macro: 0.3981, Train F1 Micro: 0.4126, Val Loss: 0.3536, Val Acc: 0.4494, Val F1 Macro: 0.4347, Val F1 Micro: 0.4434\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.3486 *****\n", "Epoch [4/12], Time: 4.31s, Train Loss: 0.3167, Train Acc: 0.5427, Train F1 Macro: 0.4333, Train F1 Micro: 0.4451, Val Loss: 0.3486, Val Acc: 0.4548, Val F1 Macro: 0.4409, Val F1 Micro: 0.4463\n", "Epoch [5/12], Time: 4.17s, Train Loss: 0.2902, Train Acc: 0.5965, Train F1 Macro: 0.4648, Train F1 Micro: 0.4754, Val Loss: 0.3578, Val Acc: 0.4478, Val F1 Macro: 0.4416, Val F1 Micro: 0.4466\n", "Epoch [6/12], Time: 4.13s, Train Loss: 0.2598, Train Acc: 0.6635, Train F1 Macro: 0.4975, Train F1 Micro: 0.5067, Val Loss: 0.3766, Val Acc: 0.4385, Val F1 Macro: 0.4404, Val F1 Micro: 0.4452\n", "Epoch [7/12], Time: 4.15s, Train Loss: 0.2274, Train Acc: 0.7222, Train F1 Macro: 0.5295, Train F1 Micro: 0.5375, Val Loss: 0.3893, Val Acc: 0.4424, Val F1 Macro: 0.4406, Val F1 Micro: 0.4448\n", "Epoch [8/12], Time: 4.10s, Train Loss: 0.1907, Train Acc: 0.7922, Train F1 Macro: 0.5624, Train F1 Micro: 0.5693, Val Loss: 0.4302, Val Acc: 0.4182, Val F1 Macro: 0.4387, Val F1 Micro: 0.4415\n", "Epoch [9/12], Time: 4.09s, Train Loss: 0.1584, Train Acc: 0.8427, Train F1 Macro: 0.5936, Train F1 Micro: 0.5997, Val Loss: 0.4668, Val Acc: 0.4229, Val F1 Macro: 0.4381, Val F1 Micro: 0.4394\n", "Epoch [10/12], Time: 4.12s, Train Loss: 0.1320, Train Acc: 0.8823, Train F1 Macro: 0.6229, Train F1 Micro: 0.6280, Val Loss: 0.4889, Val Acc: 0.4268, Val F1 Macro: 0.4378, Val F1 Micro: 0.4382\n", "Epoch [11/12], Time: 4.14s, Train Loss: 0.1091, Train Acc: 0.9168, Train F1 Macro: 0.6501, Train F1 Micro: 0.6542, Val Loss: 0.5332, Val Acc: 0.4190, Val F1 Macro: 0.4366, Val F1 Micro: 0.4364\n", "Epoch [12/12], Time: 4.20s, Train Loss: 0.0913, Train Acc: 0.9343, Train F1 Macro: 0.6739, Train F1 Micro: 0.6776, Val Loss: 0.5752, Val Acc: 0.4128, Val F1 Macro: 0.4353, Val F1 Micro: 0.4344\n", "Total Training Time: 50.24s\n", "Test Loss: 0.3479, Test Acc: 0.4459, Test F1 Macro: 0.4518, Test F1 Micro: 0.4459\n", "------------ 15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3907 *****\n", "Epoch [1/15], Time: 4.11s, Train Loss: 0.4278, Train Acc: 0.2824, Train F1 Macro: 0.2472, Train F1 Micro: 0.2824, Val Loss: 0.3907, Val Acc: 0.4198, Val F1 Macro: 0.3841, Val F1 Micro: 0.4198\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3603 *****\n", "Epoch [2/15], Time: 4.08s, Train Loss: 0.3689, Train Acc: 0.4517, Train F1 Macro: 0.3487, Train F1 Micro: 0.3670, Val Loss: 0.3603, Val Acc: 0.4447, Val F1 Macro: 0.4139, Val F1 Micro: 0.4322\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3476 *****\n", "Epoch [3/15], Time: 4.07s, Train Loss: 0.3385, Train Acc: 0.4983, Train F1 Macro: 0.3976, Train F1 Micro: 0.4108, Val Loss: 0.3476, Val Acc: 0.4688, Val F1 Macro: 0.4315, Val F1 Micro: 0.4444\n", "Epoch [4/15], Time: 4.18s, Train Loss: 0.3169, Train Acc: 0.5413, Train F1 Macro: 0.4325, Train F1 Micro: 0.4434, Val Loss: 0.3532, Val Acc: 0.4517, Val F1 Macro: 0.4360, Val F1 Micro: 0.4463\n", "Epoch [5/15], Time: 4.16s, Train Loss: 0.2940, Train Acc: 0.5960, Train F1 Macro: 0.4647, Train F1 Micro: 0.4739, Val Loss: 0.3518, Val Acc: 0.4486, Val F1 Macro: 0.4394, Val F1 Micro: 0.4467\n", "Epoch [6/15], Time: 4.12s, Train Loss: 0.2634, Train Acc: 0.6576, Train F1 Macro: 0.4965, Train F1 Micro: 0.5046, Val Loss: 0.3744, Val Acc: 0.4276, Val F1 Macro: 0.4390, Val F1 Micro: 0.4435\n", "Epoch [7/15], Time: 4.19s, Train Loss: 0.2315, Train Acc: 0.7159, Train F1 Macro: 0.5278, Train F1 Micro: 0.5348, Val Loss: 0.3867, Val Acc: 0.4369, Val F1 Macro: 0.4381, Val F1 Micro: 0.4426\n", "Epoch [8/15], Time: 4.26s, Train Loss: 0.1977, Train Acc: 0.7758, Train F1 Macro: 0.5592, Train F1 Micro: 0.5649, Val Loss: 0.4136, Val Acc: 0.4346, Val F1 Macro: 0.4378, Val F1 Micro: 0.4416\n", "Epoch [9/15], Time: 4.15s, Train Loss: 0.1648, Train Acc: 0.8313, Train F1 Macro: 0.5903, Train F1 Micro: 0.5945, Val Loss: 0.4591, Val Acc: 0.4322, Val F1 Macro: 0.4373, Val F1 Micro: 0.4406\n", "Epoch [10/15], Time: 4.24s, Train Loss: 0.1361, Train Acc: 0.8781, Train F1 Macro: 0.6198, Train F1 Micro: 0.6228, Val Loss: 0.4796, Val Acc: 0.4260, Val F1 Macro: 0.4364, Val F1 Micro: 0.4391\n", "Epoch [11/15], Time: 4.34s, Train Loss: 0.1172, Train Acc: 0.9050, Train F1 Macro: 0.6465, Train F1 Micro: 0.6485, Val Loss: 0.5238, Val Acc: 0.4338, Val F1 Macro: 0.4360, Val F1 Micro: 0.4386\n", "Epoch [12/15], Time: 4.27s, Train Loss: 0.0978, Train Acc: 0.9290, Train F1 Macro: 0.6707, Train F1 Micro: 0.6719, Val Loss: 0.5548, Val Acc: 0.4237, Val F1 Macro: 0.4349, Val F1 Micro: 0.4374\n", "Epoch [13/15], Time: 4.25s, Train Loss: 0.0839, Train Acc: 0.9464, Train F1 Macro: 0.6924, Train F1 Micro: 0.6930, Val Loss: 0.6043, Val Acc: 0.4252, Val F1 Macro: 0.4342, Val F1 Micro: 0.4364\n", "Epoch [14/15], Time: 4.24s, Train Loss: 0.0736, Train Acc: 0.9504, Train F1 Macro: 0.7112, Train F1 Micro: 0.7114, Val Loss: 0.6321, Val Acc: 0.4213, Val F1 Macro: 0.4335, Val F1 Micro: 0.4354\n", "Epoch [15/15], Time: 4.29s, Train Loss: 0.0632, Train Acc: 0.9659, Train F1 Macro: 0.7286, Train F1 Micro: 0.7283, Val Loss: 0.6713, Val Acc: 0.4167, Val F1 Macro: 0.4324, Val F1 Micro: 0.4341\n", "Total Training Time: 62.96s\n", "Test Loss: 0.3481, Test Acc: 0.4499, Test F1 Macro: 0.4494, Test F1 Micro: 0.4499\n", "------------ 17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3782 *****\n", "Epoch [1/17], Time: 4.08s, Train Loss: 0.4295, Train Acc: 0.2884, Train F1 Macro: 0.2520, Train F1 Micro: 0.2884, Val Loss: 0.3782, Val Acc: 0.4548, Val F1 Macro: 0.4340, Val F1 Micro: 0.4548\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3589 *****\n", "Epoch [2/17], Time: 4.13s, Train Loss: 0.3655, Train Acc: 0.4488, Train F1 Macro: 0.3520, Train F1 Micro: 0.3686, Val Loss: 0.3589, Val Acc: 0.4673, Val F1 Macro: 0.4419, Val F1 Micro: 0.4611\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3499 *****\n", "Epoch [3/17], Time: 4.10s, Train Loss: 0.3425, Train Acc: 0.4936, Train F1 Macro: 0.3995, Train F1 Micro: 0.4103, Val Loss: 0.3499, Val Acc: 0.4688, Val F1 Macro: 0.4540, Val F1 Micro: 0.4637\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.3480 *****\n", "Epoch [4/17], Time: 4.31s, Train Loss: 0.3204, Train Acc: 0.5393, Train F1 Macro: 0.4343, Train F1 Micro: 0.4425, Val Loss: 0.3480, Val Acc: 0.4759, Val F1 Macro: 0.4601, Val F1 Micro: 0.4667\n", "Epoch [5/17], Time: 4.06s, Train Loss: 0.2956, Train Acc: 0.5928, Train F1 Macro: 0.4660, Train F1 Micro: 0.4726, Val Loss: 0.3553, Val Acc: 0.4572, Val F1 Macro: 0.4602, Val F1 Micro: 0.4648\n", "Epoch [6/17], Time: 4.16s, Train Loss: 0.2663, Train Acc: 0.6521, Train F1 Macro: 0.4970, Train F1 Micro: 0.5025, Val Loss: 0.3682, Val Acc: 0.4509, Val F1 Macro: 0.4598, Val F1 Micro: 0.4625\n", "Epoch [7/17], Time: 4.11s, Train Loss: 0.2298, Train Acc: 0.7242, Train F1 Macro: 0.5288, Train F1 Micro: 0.5342, Val Loss: 0.3919, Val Acc: 0.4533, Val F1 Macro: 0.4599, Val F1 Micro: 0.4612\n", "Epoch [8/17], Time: 4.12s, Train Loss: 0.1938, Train Acc: 0.7840, Train F1 Macro: 0.5601, Train F1 Micro: 0.5654, Val Loss: 0.4186, Val Acc: 0.4626, Val F1 Macro: 0.4599, Val F1 Micro: 0.4614\n", "Epoch [9/17], Time: 4.26s, Train Loss: 0.1628, Train Acc: 0.8382, Train F1 Macro: 0.5906, Train F1 Micro: 0.5957, Val Loss: 0.4435, Val Acc: 0.4587, Val F1 Macro: 0.4602, Val F1 Micro: 0.4611\n", "Epoch [10/17], Time: 4.31s, Train Loss: 0.1350, Train Acc: 0.8781, Train F1 Macro: 0.6189, Train F1 Micro: 0.6239, Val Loss: 0.4839, Val Acc: 0.4642, Val F1 Macro: 0.4602, Val F1 Micro: 0.4614\n", "Epoch [11/17], Time: 4.36s, Train Loss: 0.1136, Train Acc: 0.9102, Train F1 Macro: 0.6453, Train F1 Micro: 0.6500, Val Loss: 0.5226, Val Acc: 0.4587, Val F1 Macro: 0.4602, Val F1 Micro: 0.4611\n", "Epoch [12/17], Time: 4.31s, Train Loss: 0.0938, Train Acc: 0.9350, Train F1 Macro: 0.6694, Train F1 Micro: 0.6737, Val Loss: 0.5585, Val Acc: 0.4393, Val F1 Macro: 0.4582, Val F1 Micro: 0.4593\n", "Epoch [13/17], Time: 4.22s, Train Loss: 0.0813, Train Acc: 0.9437, Train F1 Macro: 0.6906, Train F1 Micro: 0.6945, Val Loss: 0.6021, Val Acc: 0.4322, Val F1 Macro: 0.4562, Val F1 Micro: 0.4572\n", "Epoch [14/17], Time: 4.23s, Train Loss: 0.0726, Train Acc: 0.9563, Train F1 Macro: 0.7097, Train F1 Micro: 0.7132, Val Loss: 0.6360, Val Acc: 0.4377, Val F1 Macro: 0.4547, Val F1 Micro: 0.4558\n", "Epoch [15/17], Time: 4.26s, Train Loss: 0.0616, Train Acc: 0.9663, Train F1 Macro: 0.7270, Train F1 Micro: 0.7301, Val Loss: 0.6745, Val Acc: 0.4291, Val F1 Macro: 0.4531, Val F1 Micro: 0.4540\n", "Epoch [16/17], Time: 4.23s, Train Loss: 0.0551, Train Acc: 0.9706, Train F1 Macro: 0.7424, Train F1 Micro: 0.7451, Val Loss: 0.6891, Val Acc: 0.4268, Val F1 Macro: 0.4513, Val F1 Micro: 0.4523\n", "Epoch [17/17], Time: 4.42s, Train Loss: 0.0479, Train Acc: 0.9774, Train F1 Macro: 0.7564, Train F1 Micro: 0.7588, Val Loss: 0.7373, Val Acc: 0.4315, Val F1 Macro: 0.4501, Val F1 Micro: 0.4511\n", "Total Training Time: 71.67s\n", "Test Loss: 0.3528, Test Acc: 0.4420, Test F1 Macro: 0.4472, Test F1 Micro: 0.4420\n", "------------ 20\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3754 *****\n", "Epoch [1/20], Time: 4.18s, Train Loss: 0.4260, Train Acc: 0.2961, Train F1 Macro: 0.2695, Train F1 Micro: 0.2961, Val Loss: 0.3754, Val Acc: 0.4525, Val F1 Macro: 0.4106, Val F1 Micro: 0.4525\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3554 *****\n", "Epoch [2/20], Time: 4.05s, Train Loss: 0.3611, Train Acc: 0.4610, Train F1 Macro: 0.3611, Train F1 Micro: 0.3786, Val Loss: 0.3554, Val Acc: 0.4657, Val F1 Macro: 0.4416, Val F1 Micro: 0.4591\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3431 *****\n", "Epoch [3/20], Time: 4.08s, Train Loss: 0.3361, Train Acc: 0.5080, Train F1 Macro: 0.4086, Train F1 Micro: 0.4217, Val Loss: 0.3431, Val Acc: 0.4696, Val F1 Macro: 0.4515, Val F1 Micro: 0.4626\n", "***** Best Result Updated at Epoch 4, Val Loss: 0.3426 *****\n", "Epoch [4/20], Time: 4.10s, Train Loss: 0.3152, Train Acc: 0.5459, Train F1 Macro: 0.4416, Train F1 Micro: 0.4528, Val Loss: 0.3426, Val Acc: 0.4650, Val F1 Macro: 0.4522, Val F1 Micro: 0.4632\n", "Epoch [5/20], Time: 4.12s, Train Loss: 0.2895, Train Acc: 0.5980, Train F1 Macro: 0.4714, Train F1 Micro: 0.4818, Val Loss: 0.3548, Val Acc: 0.4603, Val F1 Macro: 0.4561, Val F1 Micro: 0.4626\n", "Epoch [6/20], Time: 4.11s, Train Loss: 0.2596, Train Acc: 0.6614, Train F1 Macro: 0.5013, Train F1 Micro: 0.5118, Val Loss: 0.3660, Val Acc: 0.4579, Val F1 Macro: 0.4584, Val F1 Micro: 0.4618\n", "Epoch [7/20], Time: 4.08s, Train Loss: 0.2255, Train Acc: 0.7319, Train F1 Macro: 0.5329, Train F1 Micro: 0.5432, Val Loss: 0.3760, Val Acc: 0.4540, Val F1 Macro: 0.4579, Val F1 Micro: 0.4607\n", "Epoch [8/20], Time: 4.08s, Train Loss: 0.1919, Train Acc: 0.7903, Train F1 Macro: 0.5637, Train F1 Micro: 0.5741, Val Loss: 0.4020, Val Acc: 0.4393, Val F1 Macro: 0.4555, Val F1 Micro: 0.4580\n", "Epoch [9/20], Time: 4.07s, Train Loss: 0.1581, Train Acc: 0.8427, Train F1 Macro: 0.5939, Train F1 Micro: 0.6039, Val Loss: 0.4392, Val Acc: 0.4478, Val F1 Macro: 0.4552, Val F1 Micro: 0.4569\n", "Epoch [10/20], Time: 4.07s, Train Loss: 0.1326, Train Acc: 0.8805, Train F1 Macro: 0.6222, Train F1 Micro: 0.6316, Val Loss: 0.4684, Val Acc: 0.4517, Val F1 Macro: 0.4541, Val F1 Micro: 0.4564\n", "Epoch [11/20], Time: 4.21s, Train Loss: 0.1105, Train Acc: 0.9112, Train F1 Macro: 0.6484, Train F1 Micro: 0.6570, Val Loss: 0.5092, Val Acc: 0.4439, Val F1 Macro: 0.4535, Val F1 Micro: 0.4553\n", "Epoch [12/20], Time: 4.28s, Train Loss: 0.0900, Train Acc: 0.9356, Train F1 Macro: 0.6725, Train F1 Micro: 0.6802, Val Loss: 0.5555, Val Acc: 0.4322, Val F1 Macro: 0.4519, Val F1 Micro: 0.4533\n", "Epoch [13/20], Time: 4.23s, Train Loss: 0.0779, Train Acc: 0.9495, Train F1 Macro: 0.6940, Train F1 Micro: 0.7009, Val Loss: 0.5799, Val Acc: 0.4354, Val F1 Macro: 0.4504, Val F1 Micro: 0.4520\n", "Epoch [14/20], Time: 4.15s, Train Loss: 0.0683, Train Acc: 0.9588, Train F1 Macro: 0.7131, Train F1 Micro: 0.7194, Val Loss: 0.6171, Val Acc: 0.4346, Val F1 Macro: 0.4490, Val F1 Micro: 0.4507\n", "Epoch [15/20], Time: 4.22s, Train Loss: 0.0604, Train Acc: 0.9667, Train F1 Macro: 0.7303, Train F1 Micro: 0.7359, Val Loss: 0.6363, Val Acc: 0.4354, Val F1 Macro: 0.4483, Val F1 Micro: 0.4497\n", "Epoch [16/20], Time: 4.20s, Train Loss: 0.0524, Train Acc: 0.9728, Train F1 Macro: 0.7456, Train F1 Micro: 0.7507, Val Loss: 0.6804, Val Acc: 0.4315, Val F1 Macro: 0.4474, Val F1 Micro: 0.4485\n", "Epoch [17/20], Time: 4.27s, Train Loss: 0.0491, Train Acc: 0.9749, Train F1 Macro: 0.7593, Train F1 Micro: 0.7638, Val Loss: 0.7106, Val Acc: 0.4291, Val F1 Macro: 0.4463, Val F1 Micro: 0.4474\n", "Epoch [18/20], Time: 4.31s, Train Loss: 0.0433, Train Acc: 0.9782, Train F1 Macro: 0.7717, Train F1 Micro: 0.7758, Val Loss: 0.7423, Val Acc: 0.4299, Val F1 Macro: 0.4451, Val F1 Micro: 0.4464\n", "Epoch [19/20], Time: 4.22s, Train Loss: 0.0391, Train Acc: 0.9824, Train F1 Macro: 0.7829, Train F1 Micro: 0.7866, Val Loss: 0.7723, Val Acc: 0.4237, Val F1 Macro: 0.4439, Val F1 Micro: 0.4452\n", "Epoch [20/20], Time: 4.20s, Train Loss: 0.0356, Train Acc: 0.9853, Train F1 Macro: 0.7931, Train F1 Micro: 0.7966, Val Loss: 0.8003, Val Acc: 0.4268, Val F1 Macro: 0.4428, Val F1 Micro: 0.4443\n", "Total Training Time: 83.23s\n", "Test Loss: 0.3491, Test Acc: 0.4451, Test F1 Macro: 0.4379, Test F1 Micro: 0.4451\n"]}], "source": ["for num_epochs in [3,5,7,9,12,15,17,20]:\n", "    print(\"------------\",num_epochs)\n", "    vocab_size = 30522\n", "    embedding_dim = 128\n", "    n_filters = 128\n", "    filter_sizes = [3,4,5]\n", "    output_dim = 6\n", "    dropout = 0.5\n", "    padding_idx = 0\n", "    input_dim = 6 * metadata_each_dim\n", "    input_dim_metadata = 5\n", "    hidden_dim = 64\n", "    n_layers = 1\n", "    bidirectional = True\n", "\n", "    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "    # Define the optimizer and loss function\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "    # Record the training process\n", "    Train_acc = []\n", "    Train_loss = []\n", "    Train_macro_f1 = []\n", "    Train_micro_f1 = []\n", "\n", "    Val_acc = []\n", "    Val_loss = []\n", "    Val_macro_f1 = []\n", "    Val_micro_f1 = []\n", "\n", "    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "        epoch_trained = 0\n", "        train_label_all = []\n", "        train_predict_all = []\n", "        val_label_all = []\n", "        val_predict_all = []\n", "        best_valid_loss = float('inf')\n", "\n", "        start_time = time.time()\n", "        for epoch in range(num_epochs):\n", "            epoch_trained += 1\n", "            epoch_start_time = time.time()\n", "            # Training\n", "            model.train()\n", "            train_loss = 0.0\n", "            train_accuracy = 0.0\n", "            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                optimizer.zero_grad()\n", "                outputs = model(statements, metadata_text, metadata_number)\n", "                loss = criterion(outputs, label_onehot)\n", "                loss.backward()\n", "                optimizer.step()\n", "\n", "                train_loss += loss.item()\n", "                _, train_predicted = torch.max(outputs, 1)\n", "                train_accuracy += sum(train_predicted == label)\n", "                train_predict_all += train_predicted.tolist()\n", "                train_label_all += label.tolist()\n", "            train_loss /= len(train_loader)\n", "            train_accuracy /= len(train_loader.dataset)\n", "            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "            Train_acc.append(train_accuracy.tolist())\n", "            Train_loss.append(train_loss)\n", "            Train_macro_f1.append(train_macro_f1)\n", "            Train_micro_f1.append(train_micro_f1)\n", "\n", "            # Validation\n", "            model.eval()\n", "            val_loss = 0.0\n", "            val_accuracy = 0.0\n", "            with torch.no_grad():\n", "                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                    statements = statements.to(DEVICE)\n", "                    label_onehot = label_onehot.to(DEVICE)\n", "                    label = label.to(DEVICE)\n", "                    metadata_text = metadata_text.to(DEVICE)\n", "                    metadata_number = metadata_number.to(DEVICE)\n", "\n", "                    val_outputs = model(statements, metadata_text, metadata_number)\n", "                    val_loss += criterion(val_outputs, label_onehot).item()\n", "                    _, val_predicted = torch.max(val_outputs, 1)\n", "                    val_accuracy += sum(val_predicted == label)\n", "                    val_predict_all += val_predicted.tolist()\n", "                    val_label_all += label.tolist()\n", "            val_loss /= len(val_loader)\n", "            val_accuracy /= len(val_loader.dataset)\n", "            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "            Val_acc.append(val_accuracy.tolist())\n", "            Val_loss.append(val_loss)\n", "            Val_macro_f1.append(val_macro_f1)\n", "            Val_micro_f1.append(val_micro_f1)\n", "\n", "            if val_loss < best_valid_loss:\n", "                best_valid_loss = val_loss\n", "                torch.save(model.state_dict(), model_save)\n", "                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "            # Print the losses and accuracy\n", "            epoch_end_time = time.time()\n", "            epoch_time = epoch_end_time - epoch_start_time\n", "\n", "            print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "        end_time = time.time()\n", "        training_time = end_time - start_time\n", "        print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "    # Evaluate the model on new data\n", "    def test(model, test_loader, model_save):\n", "        model.load_state_dict(torch.load(model_save))\n", "        model.eval()\n", "\n", "        test_label_all = []\n", "        test_predict_all = []\n", "\n", "        test_loss = 0.0\n", "        test_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                test_outputs = model(statements, metadata_text, metadata_number)\n", "                test_loss += criterion(test_outputs, label_onehot).item()\n", "                _, test_predicted = torch.max(test_outputs, 1)\n", "                \n", "                test_accuracy += sum(test_predicted == label)\n", "                test_predict_all += test_predicted.tolist()\n", "                test_label_all += label.tolist()\n", "\n", "        test_loss /= len(test_loader)\n", "        test_accuracy /= len(test_loader.dataset)\n", "        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "    test(model, test_loader, model_save)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "TEXTGCN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}