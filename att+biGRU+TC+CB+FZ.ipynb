{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 最终版"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-07-12T00:15:00.830616Z", "start_time": "2025-07-12T00:15:00.807586Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 1.11.0+cpu\n", "cpu\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'E:/liuhui/ljl/FAKE/FAKE/liar_dataset/train.tsv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 60\u001b[0m\n\u001b[0;32m     58\u001b[0m label_convert \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m2\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m3\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m:\u001b[38;5;241m5\u001b[39m}\n\u001b[0;32m     59\u001b[0m \u001b[38;5;66;03m# 加载数据集，从指定路径加载训练集、测试集和验证集。\u001b[39;00m\n\u001b[1;32m---> 60\u001b[0m train_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworksapce\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtrain.tsv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;130;43;01m\\t\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnames\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     61\u001b[0m test_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtest.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n\u001b[0;32m     62\u001b[0m val_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvalid.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n", "File \u001b[1;32md:\\Anaconda\\envs\\py_39\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Anaconda\\envs\\py_39\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32md:\\Anaconda\\envs\\py_39\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Anaconda\\envs\\py_39\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1881\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1882\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1883\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1884\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1885\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1886\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1887\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1888\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1889\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32md:\\Anaconda\\envs\\py_39\\lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'E:/liuhui/ljl/FAKE/FAKE/liar_dataset/train.tsv'"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "#导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "#设置随机种子，确保实验可重复\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "\n", "#设备配置\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "\n", "#路径和模型配置\n", "#worksapce = 'D:/code/HLAN/FAKE/liar_dataset/'\n", "worksapce = 'E:/liuhui/ljl/FAKE/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "#训练超参数：定义训练超参数（轮数、批大小、学习率等）\n", "#配置模型参数（类别数、填充索引等）\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "#数据加载和预处理\n", "#定义了数据集中每一列的名称\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "#定义了标签的映射关系，方便在数值和字符串之间进行转换（正向映射和反向映射）\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "# 加载数据集，从指定路径加载训练集、测试集和验证集。\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "# 处理缺失值，数据集中缺失值：数值型用0，非数值型用'NaN'\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "# def textProcess(input_text, max_length = -1):\n", "#     tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "#     if max_length == -1:\n", "#         tokens = tokenizer(input_text, truncation=True, padding=True)\n", "#     else:\n", "#         tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "#     return tokens\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    #input_text：需要处理的输入文本\n", "    #max_length：可选参数，默认值为 -1，表示是否对文本长度进行限制。\n", "    # 如果未指定，则不设置最大长度；如果指定了具体值，则将文本截断或填充到该长度。\n", "    local_model_path = 'E:/liuhui/ljl/FAKE/FAKE/bert-base-uncased'\n", "    tokenizer = BertTokenizer.from_pretrained(local_model_path, local_files_only=True)\n", "    #文本分词并转换为模型所需的输入格式\n", "    #参数 local_files_only=True 表示只使用本地文件，不会尝试从网络下载模型文件。\n", "    #根据输入文本和参数设置，对文本进行分词、截断和填充\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "\n", "\n", "\n", "# 处理训练数据\n", "# Define the data loaders for training and validation\n", "#对训练集中的 statement（新闻文本）进行分词和编码\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "#将标签从字符串形式（如 'pants-fire'）转换为 one-hot 编码形式。\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "#对元数据字段（如 subject、speaker 等）进行分词和编码。\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "#将所有特征和标签封装到自定义的 LiarDataset 类中。\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MODEL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### capsnet"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["from torch.autograd import Variable\n", "import math\n", "class ConvLayer(nn.Module):\n", "    \"\"\" N-gram convolutional layer\n", "    \n", "        Args:\n", "            in_channels: convolutional input channels\n", "            out_channels: convolutional output channels\n", "            kernel_size: convolutional kernel size\n", "            stride: convolutional stride\n", "    \n", "    \"\"\"\n", "    def __init__(self, in_channels, out_channels, kernel_size, stride):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.convnet = nn.Sequential(\n", "            nn.Conv1d(\n", "                in_channels=in_channels, \n", "                out_channels=out_channels, \n", "                kernel_size=kernel_size,\n", "                stride=stride\n", "            ),\n", "            nn.ELU(),\n", "        )\n", "\n", "    def forward(self, x):\n", "        return self.convnet(x)\n", "    \n", "def calculate_conv_output(input_, kernel, padding, stride):\n", "    \"\"\"Calculate the output size in Convolution layer\"\"\"\n", "    return math.floor(((input_ - kernel + 2 * padding) / stride) + 1)\n", "class PrimaryCaps(nn.Module):\n", "    \"\"\" Primary caps layer\n", "    \n", "    \n", "        Args:\n", "            num_capsules: capsules count\n", "            in_channels: input channels\n", "            out_channels: output channels\n", "            kernel_size: capsule kernel size\n", "            stride: capsule stride\n", "            conv_out_size: tensor size from above layer\n", "\n", "    \"\"\"\n", "    def __init__(self, num_capsules, in_channels, out_channels, kernel_size, stride, conv_out_size):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.out_channels = out_channels\n", "        self.capsules = nn.ModuleList([\n", "            nn.Conv1d(\n", "                in_channels=in_channels, \n", "                out_channels=out_channels, \n", "                kernel_size=kernel_size, \n", "                stride=stride, \n", "                padding=0\n", "            ) \n", "            for _ in range(num_capsules)\n", "        ])\n", "        \n", "        self._out_channels = out_channels\n", "        self._conv_out_size = conv_out_size\n", "    \n", "    def forward(self, x):\n", "        u = [capsule(x) for capsule in self.capsules]\n", "        u = torch.stack(u, dim=1)\n", "        u = u.view(x.size(0), self._out_channels * self._conv_out_size , -1)\n", "\n", "        return self.squash(u)\n", "    \n", "    def squash(self, input_tensor):\n", "        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)\n", "        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))\n", "\n", "        return output_tensor\n", "\n", "class SecondaryCaps(nn.Module):\n", "    \"\"\" Secondary capsules layer\n", "    \n", "        Args:\n", "            num_capsules: capsules count\n", "            num_routes: routing iteration count\n", "            in_channels: in channels dim\n", "            out_channels: out channels dim\n", "    \n", "    \"\"\"\n", "    def __init__(self, num_capsules, num_routes, in_channels, out_channels):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.in_channels = in_channels\n", "        self.num_routes = num_routes\n", "        self.num_capsules = num_capsules\n", "\n", "        self.W = nn.Parameter(torch.randn(1, num_routes, num_capsules, out_channels, in_channels))\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = torch.stack([x] * self.num_capsules, dim=2).unsqueeze(4)\n", "\n", "        W = torch.cat([self.W] * batch_size, dim=0)\n", "        # print(x.shape, W.shape)\n", "        u_hat = torch.matmul(W, x)\n", "\n", "        b_ij = Variable(torch.zeros(1, self.num_routes, self.num_capsules, 1)).to(cuda)\n", "\n", "        num_iterations = 3\n", "        for iteration in range(num_iterations):\n", "            c_ij = <PERSON>.softmax(b_ij, dim=2)\n", "\n", "            c_ij = torch.cat([c_ij] * batch_size, dim=0).unsqueeze(4)\n", "\n", "            s_j = (c_ij * u_hat).sum(dim=1, keepdim=True)\n", "            v_j = self.squash(s_j)\n", "            \n", "            if iteration < num_iterations - 1:\n", "                a_ij = torch.matmul(u_hat.transpose(3, 4), torch.cat([v_j] * self.num_routes, dim=1))\n", "                b_ij = b_ij + a_ij.squeeze(4).mean(dim=0, keepdim=True)\n", "\n", "        return v_j.squeeze(1)\n", "    \n", "    def squash(self, input_tensor):\n", "        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)\n", "        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))\n", "\n", "        return output_tensor\n", "    \n", "class CapsNet(nn.Module):\n", "    \"\"\" Caps net module of CBAE\n", "    \n", "        Args:\n", "            conv_out_ch: N-gram convolutional out channels\n", "            conv_kernel: N-gram convolutional kernel size\n", "            conv_stride: N-gram convolutional stride\n", "            prime_num_capsules: Primary capsules count\n", "            prime_out_ch: Primary capsules out channels\n", "            prime_kernel: Primary capsules kernel size\n", "            prime_stride: Primary capsules stride\n", "            secondary_num_capsules: Secondary capsules count\n", "            secondary_out_channels=Secondary capsules out channels\n", "            maxlen: sentence max length taken into account\n", "    \n", "    \"\"\"\n", "    def __init__(\n", "        self,\n", "        conv_in_ch,\n", "        conv_out_ch,\n", "        conv_kernel,\n", "        conv_stride,\n", "        prime_num_capsules,\n", "        prime_out_ch,\n", "        prime_kernel,\n", "        prime_stride,\n", "        secondary_num_capsules,\n", "        secondary_out_channels,\n", "        input_len,\n", "    ):\n", "        super(CapsNet, self).__init__()\n", "        self._secondary_out_size=secondary_out_channels * secondary_num_capsules\n", "        \n", "        self.conv_layer = ConvLayer(\n", "            in_channels=conv_in_ch,\n", "            out_channels=conv_out_ch,\n", "            kernel_size=conv_kernel,\n", "            stride=conv_stride,\n", "        )\n", "        conv_layer_output = calculate_conv_output(\n", "            input_=input_len, \n", "            kernel=conv_kernel, \n", "            padding=0, \n", "            stride=conv_stride,\n", "        )\n", "        \n", "        prime_caps_conv_output = calculate_conv_output(\n", "            input_=conv_layer_output, \n", "            kernel=prime_kernel, \n", "            padding=0, \n", "            stride=prime_stride,\n", "        )\n", "        \n", "        self.primary_caps = PrimaryCaps(\n", "            num_capsules=prime_num_capsules, \n", "            in_channels=conv_out_ch, \n", "            out_channels=prime_out_ch, \n", "            kernel_size=prime_kernel, \n", "            stride=prime_stride,\n", "            conv_out_size=prime_caps_conv_output,\n", "        )\n", "        \n", "        self.secondary_caps = SecondaryCaps(\n", "            num_capsules=secondary_num_capsules,\n", "            num_routes=prime_caps_conv_output * prime_out_ch,\n", "            in_channels=prime_num_capsules,\n", "            out_channels=secondary_out_channels,\n", "        )\n", "\n", "        self.fc = nn.Linear(secondary_out_channels * secondary_num_capsules, input_len)\n", "        self.tanh = nn.Tanh()\n", "        self.capsule_softmax = torch.nn.Softmax(dim=1)\n", "\n", "    def forward(self, data):\n", "        output = self.secondary_caps(self.primary_caps(self.conv_layer(data)))\n", "        output = output.reshape(-1, self._secondary_out_size)\n", "        output = F.relu(output)\n", "        output = self.fc(output)\n", "        return self.capsule_softmax(self.tanh(output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### attention"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["from torch.nn.parameter import Parameter\n", "from torch.nn import init\n", "class SelfAttention(torch.nn.<PERSON>):\n", "    \"\"\"Self attention\n", "    \n", "    Args:\n", "        wv_dim: word vector sizeluence\n", "        maxlen: sentence max length taken into account\n", "    \n", "    \"\"\"\n", "    def __init__(self, wv_dim, maxlen):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.wv_dim = wv_dim\n", "\n", "        # max sentence length -- batch 2nd dim size\n", "        self.maxlen = maxlen\n", "        self.M = Parameter(torch.empty(size=(wv_dim, wv_dim)))\n", "        init.kaiming_uniform_(self.M.data)\n", "\n", "        self.tanh = nn.Tanh()\n", "        self.attention_softmax = torch.nn.Softmax(dim=1)\n", "\n", "    def forward(self, input_embeddings):\n", "        # (b, wv, 1)\n", "        mean_embedding = torch.mean(input_embeddings, (1,)).unsqueeze(2)\n", "\n", "        # (wv, wv) x (b, wv, 1) -> (b, wv, 1)\n", "        product_1 = torch.matmul(self.M, mean_embedding)\n", "\n", "        # (b, maxlen, wv) x (b, wv, 1) -> (b, maxlen, 1)\n", "        product_2 = torch.matmul(input_embeddings, product_1).squeeze(2)\n", "\n", "        results = self.attention_softmax(self.tanh(product_2))\n", "\n", "        return results\n", "\n", "    def extra_repr(self):\n", "        return 'wv_dim={}, maxlen={}'.format(self.wv_dim, self.maxlen)\n", "    \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### KAN"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["# below from .spline import *\n", "import torch\n", "\n", "\n", "def B_batch(x, grid, k=0, extend=True, device='cpu'):\n", "    '''\n", "    evaludate x on B-spline bases\n", "    \n", "    Args:\n", "    -----\n", "        x : 2D torch.tensor\n", "            inputs, shape (number of splines, number of samples)\n", "        grid : 2D torch.tensor\n", "            grids, shape (number of splines, number of grid points)\n", "        k : int\n", "            the piecewise polynomial order of splines.\n", "        extend : bool\n", "            If True, k points are extended on both ends. If False, no extension (zero boundary condition). Default: True\n", "        device : str\n", "            devicde\n", "    \n", "    Returns:\n", "    --------\n", "        spline values : 3D torch.tensor\n", "            shape (number of splines, number of B-spline bases (coeffcients), number of samples). The numbef of B-spline bases = number of grid points + k - 1.\n", "      \n", "    Example\n", "    -------\n", "    >>> num_spline = 5\n", "    >>> num_sample = 100\n", "    >>> num_grid_interval = 10\n", "    >>> k = 3\n", "    >>> x = torch.normal(0,1,size=(num_spline, num_sample))\n", "    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))\n", "    >>> B_batch(x, grids, k=k).shape\n", "    torch.<PERSON><PERSON>([5, 13, 100])\n", "    '''\n", "\n", "    # x shape: (size, x); grid shape: (size, grid)\n", "    def extend_grid(grid, k_extend=0):\n", "        # pad k to left and right\n", "        # grid shape: (batch, grid)\n", "        h = (grid[:, [-1]] - grid[:, [0]]) / (grid.shape[1] - 1)\n", "\n", "        for i in range(k_extend):\n", "            grid = torch.cat([grid[:, [0]] - h, grid], dim=1)\n", "            grid = torch.cat([grid, grid[:, [-1]] + h], dim=1)\n", "        grid = grid.to(device)\n", "        return grid\n", "\n", "    if extend == True:\n", "        grid = extend_grid(grid, k_extend=k)\n", "\n", "    grid = grid.unsqueeze(dim=2).to(device)\n", "    x = x.unsqueeze(dim=1).to(device)\n", "\n", "    if k == 0:\n", "        value = (x >= grid[:, :-1]) * (x < grid[:, 1:])\n", "    else:\n", "        B_km1 = B_batch(x[:, 0], grid=grid[:, :, 0], k=k - 1, extend=False, device=device)\n", "        value = (x - grid[:, :-(k + 1)]) / (grid[:, k:-1] - grid[:, :-(k + 1)]) * B_km1[:, :-1] + (grid[:, k + 1:] - x) / (grid[:, k + 1:] - grid[:, 1:(-k)]) * B_km1[:, 1:]\n", "    return value\n", "\n", "\n", "def coef2curve(x_eval, grid, coef, k, device=\"cpu\"):\n", "    '''\n", "    converting B-spline coefficients to B-spline curves. Evaluate x on B-spline curves (summing up B_batch results over B-spline basis).\n", "    \n", "    Args:\n", "    -----\n", "        x_eval : 2D torch.tensor)\n", "            shape (number of splines, number of samples)\n", "        grid : 2D torch.tensor)\n", "            shape (number of splines, number of grid points)\n", "        coef : 2D torch.tensor)\n", "            shape (number of splines, number of coef params). number of coef params = number of grid intervals + k\n", "        k : int\n", "            the piecewise polynomial order of splines.\n", "        device : str\n", "            devicde\n", "        \n", "    Returns:\n", "    --------\n", "        y_eval : 2D torch.tensor\n", "            shape (number of splines, number of samples)\n", "        \n", "    Example\n", "    -------\n", "    >>> num_spline = 5\n", "    >>> num_sample = 100\n", "    >>> num_grid_interval = 10\n", "    >>> k = 3\n", "    >>> x_eval = torch.normal(0,1,size=(num_spline, num_sample))\n", "    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))\n", "    >>> coef = torch.normal(0,1,size=(num_spline, num_grid_interval+k))\n", "    >>> coef2curve(x_eval, grids, coef, k=k).shape\n", "    torch.Size([5, 100])\n", "    '''\n", "    # x_eval: (size, batch), grid: (size, grid), coef: (size, coef)\n", "    # coef: (size, coef), B_batch: (size, coef, batch), summer over coef\n", "    if coef.dtype != x_eval.dtype:\n", "        coef = coef.to(x_eval.dtype)\n", "    y_eval = torch.einsum('ij,ijk->ik', coef, B_batch(x_eval, grid, k, device=device))\n", "    return y_eval\n", "\n", "\n", "def curve2coef(x_eval, y_eval, grid, k, device=\"cpu\"):\n", "    '''\n", "    converting B-spline curves to B-spline coefficients using least squares.\n", "    \n", "    Args:\n", "    -----\n", "        x_eval : 2D torch.tensor\n", "            shape (number of splines, number of samples)\n", "        y_eval : 2D torch.tensor\n", "            shape (number of splines, number of samples)\n", "        grid : 2D torch.tensor\n", "            shape (number of splines, number of grid points)\n", "        k : int\n", "            the piecewise polynomial order of splines.\n", "        device : str\n", "            devicde\n", "        \n", "    Example\n", "    -------\n", "    >>> num_spline = 5\n", "    >>> num_sample = 100\n", "    >>> num_grid_interval = 10\n", "    >>> k = 3\n", "    >>> x_eval = torch.normal(0,1,size=(num_spline, num_sample))\n", "    >>> y_eval = torch.normal(0,1,size=(num_spline, num_sample))\n", "    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))\n", "    >>> curve2coef(x_eval, y_eval, grids, k=k).shape\n", "    torch.<PERSON><PERSON>([5, 13])\n", "    '''\n", "    # x_eval: (size, batch); y_eval: (size, batch); grid: (size, grid); k: scalar\n", "    mat = B_batch(x_eval, grid, k, device=device).permute(0, 2, 1).to(y_eval.dtype)\n", "    coef = torch.linalg.lstsq(mat.to('cpu'), y_eval.unsqueeze(dim=2).to('cpu')).solution[:, :, 0]  # sometimes 'cuda' version may diverge\n", "    return coef.to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "# from .spline import *\n", "\n", "\n", "class KANLayer(nn.<PERSON><PERSON>):\n", "    \"\"\"\n", "    KANLayer class\n", "    \n", "\n", "    Attributes:\n", "    -----------\n", "        in_dim: int\n", "            input dimension\n", "        out_dim: int\n", "            output dimension\n", "        size: int\n", "            the number of splines = input dimension * output dimension\n", "        k: int\n", "            the piecewise polynomial order of splines\n", "        grid: 2D torch.float\n", "            grid points\n", "        noises: 2D torch.float\n", "            injected noises to splines at initialization (to break degeneracy)\n", "        coef: 2D torch.tensor\n", "            coefficients of B-spline bases\n", "        scale_base: 1D torch.float\n", "            magnitude of the residual function b(x)\n", "        scale_sp: 1D torch.float\n", "            mangitude of the spline function spline(x)\n", "        base_fun: fun\n", "            residual function b(x)\n", "        mask: 1D torch.float\n", "            mask of spline functions. setting some element of the mask to zero means setting the corresponding activation to zero function.\n", "        grid_eps: float in [0,1]\n", "            a hyperparameter used in update_grid_from_samples. When grid_eps = 0, the grid is uniform; when grid_eps = 1, the grid is partitioned using percentiles of samples. 0 < grid_eps < 1 interpolates between the two extremes.\n", "        weight_sharing: 1D tensor int\n", "            allow spline activations to share parameters\n", "        lock_counter: int\n", "            counter how many activation functions are locked (weight sharing)\n", "        lock_id: 1D torch.int\n", "            the id of activation functions that are locked\n", "        device: str\n", "            device\n", "    \n", "    Methods:\n", "    --------\n", "        __init__():\n", "            initialize a KANLayer\n", "        forward():\n", "            forward \n", "        update_grid_from_samples():\n", "            update grids based on samples' incoming activations\n", "        initialize_grid_from_parent():\n", "            initialize grids from another model\n", "        get_subset():\n", "            get subset of the KANLayer (used for pruning)\n", "        lock():\n", "            lock several activation functions to share parameters\n", "        unlock():\n", "            unlock already locked activation functions\n", "    \"\"\"\n", "\n", "    def __init__(self, in_dim=3, out_dim=2, num=5, k=3, noise_scale=0.1, scale_base=1.0, scale_sp=1.0, base_fun=torch.nn.SiLU(), grid_eps=0.02, grid_range=[-1, 1], sp_trainable=True, sb_trainable=True, device='cpu'):\n", "        ''''\n", "        initialize a KANLayer\n", "        \n", "        Args:\n", "        -----\n", "            in_dim : int\n", "                input dimension. Default: 2.\n", "            out_dim : int\n", "                output dimension. Default: 3.\n", "            num : int\n", "                the number of grid intervals = <PERSON><PERSON>: 5.\n", "            k : int\n", "                the order of piecewise polynomial. De<PERSON><PERSON>: 3.\n", "            noise_scale : float\n", "                the scale of noise injected at initialization. Default: 0.1.\n", "            scale_base : float\n", "                the scale of the residual function b(x). Default: 1.0.\n", "            scale_sp : float\n", "                the scale of the base function spline(x). Default: 1.0.\n", "            base_fun : function\n", "                residual function b(x). Default: torch.nn.SiLU()\n", "            grid_eps : float\n", "                When grid_eps = 0, the grid is uniform; when grid_eps = 1, the grid is partitioned using percentiles of samples. 0 < grid_eps < 1 interpolates between the two extremes. Default: 0.02.\n", "            grid_range : list/np.array of shape (2,)\n", "                setting the range of grids. Default: [-1,1].\n", "            sp_trainable : bool\n", "                If true, scale_sp is trainable. Default: True.\n", "            sb_trainable : bool\n", "                If true, scale_base is trainable. Default: True.\n", "            device : str\n", "                device\n", "            \n", "        Returns:\n", "        --------\n", "            self\n", "            \n", "        Example\n", "        -------\n", "        >>> model = KANLayer(in_dim=3, out_dim=5)\n", "        >>> (model.in_dim, model.out_dim)\n", "        (3, 5)\n", "        '''\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        # size \n", "        self.size = size = out_dim * in_dim\n", "        self.out_dim = out_dim\n", "        self.in_dim = in_dim\n", "        self.num = num\n", "        self.k = k\n", "\n", "        # shape: (size, num)\n", "        self.grid = torch.einsum('i,j->ij', torch.ones(size, device=device), torch.linspace(grid_range[0], grid_range[1], steps=num + 1, device=device))\n", "        self.grid = torch.nn.Parameter(self.grid).requires_grad_(False)\n", "        noises = (torch.rand(size, self.grid.shape[1]) - 1 / 2) * noise_scale / num\n", "        noises = noises.to(device)\n", "        # shape: (size, coef)\n", "        self.coef = torch.nn.Parameter(curve2coef(self.grid, noises, self.grid, k, device))\n", "        if isinstance(scale_base, float):\n", "            self.scale_base = torch.nn.Parameter(torch.ones(size, device=device) * scale_base).requires_grad_(sb_trainable)  # make scale trainable\n", "        else:\n", "            self.scale_base = torch.nn.Parameter(torch.FloatTensor(scale_base).to(device)).requires_grad_(sb_trainable)\n", "        self.scale_sp = torch.nn.Parameter(torch.ones(size, device=device) * scale_sp).requires_grad_(sp_trainable)  # make scale trainable\n", "        self.base_fun = base_fun\n", "\n", "        self.mask = torch.nn.Parameter(torch.ones(size, device=device)).requires_grad_(False)\n", "        self.grid_eps = grid_eps\n", "        self.weight_sharing = torch.arange(size)\n", "        self.lock_counter = 0\n", "        self.lock_id = torch.zeros(size)\n", "        self.device = \"cuda\"\n", "\n", "    def forward(self, x):\n", "        '''\n", "        KANLayer forward given input x\n", "        \n", "        Args:\n", "        -----\n", "            x : 2D torch.float\n", "                inputs, shape (number of samples, input dimension)\n", "            \n", "        Returns:\n", "        --------\n", "            y : 2D torch.float\n", "                outputs, shape (number of samples, output dimension)\n", "            preacts : 3D torch.float\n", "                fan out x into activations, shape (number of sampels, output dimension, input dimension)\n", "            postacts : 3D torch.float\n", "                the outputs of activation functions with preacts as inputs\n", "            postspline : 3D torch.float\n", "                the outputs of spline functions with preacts as inputs\n", "        \n", "        Example\n", "        -------\n", "        >>> model = KANLayer(in_dim=3, out_dim=5)\n", "        >>> x = torch.normal(0,1,size=(100,3))\n", "        >>> y, preacts, postacts, postspline = model(x)\n", "        >>> y.shape, preacts.shape, postacts.shape, postspline.shape\n", "        (torch.<PERSON><PERSON>([100, 5]),\n", "         torch.<PERSON><PERSON>([100, 5, 3]),\n", "         torch.<PERSON><PERSON>([100, 5, 3]),\n", "         torch.<PERSON><PERSON>([100, 5, 3]))\n", "        '''\n", "        batch = x.shape[0]\n", "        # x: shape (batch, in_dim) => shape (size, batch) (size = out_dim * in_dim)\n", "        x = torch.einsum('ij,k->ikj', x, torch.ones(self.out_dim, device=self.device)).reshape(batch, self.size).permute(1, 0)\n", "        preacts = x.permute(1, 0).clone().reshape(batch, self.out_dim, self.in_dim)\n", "        base = self.base_fun(x).permute(1, 0)  # shape (batch, size)\n", "        y = coef2curve(x_eval=x, grid=self.grid[self.weight_sharing], coef=self.coef[self.weight_sharing], k=self.k, device=self.device)  # shape (size, batch)\n", "        y = y.permute(1, 0)  # shape (batch, size)\n", "        postspline = y.clone().reshape(batch, self.out_dim, self.in_dim)\n", "        y = self.scale_base.unsqueeze(dim=0) * base + self.scale_sp.unsqueeze(dim=0) * y\n", "        y = self.mask[None, :] * y\n", "        postacts = y.clone().reshape(batch, self.out_dim, self.in_dim)\n", "        y = torch.sum(y.reshape(batch, self.out_dim, self.in_dim), dim=2)  # shape (batch, out_dim)\n", "        # y shape: (batch, out_dim); preacts shape: (batch, in_dim, out_dim)\n", "        # postspline shape: (batch, in_dim, out_dim); postacts: (batch, in_dim, out_dim)\n", "        # postspline is for extension; postacts is for visualization\n", "        return y, preacts, postacts, postspline\n", "\n", "    def update_grid_from_samples(self, x):\n", "        '''\n", "        update grid from samples\n", "        \n", "        Args:\n", "        -----\n", "            x : 2D torch.float\n", "                inputs, shape (number of samples, input dimension)\n", "            \n", "        Returns:\n", "        --------\n", "            None\n", "        \n", "        Example\n", "        -------\n", "        >>> model = KANLayer(in_dim=1, out_dim=1, num=5, k=3)\n", "        >>> print(model.grid.data)\n", "        >>> x = torch.linspace(-3,3,steps=100)[:,None]\n", "        >>> model.update_grid_from_samples(x)\n", "        >>> print(model.grid.data)\n", "        tensor([[-1.0000, -0.6000, -0.2000,  0.2000,  0.6000,  1.0000]])\n", "        tensor([[-3.0002, -1.7882, -0.5763,  0.6357,  1.8476,  3.0002]])\n", "        '''\n", "        batch = x.shape[0]\n", "        x = torch.einsum('ij,k->ikj', x, torch.ones(self.out_dim, ).to(self.device)).reshape(batch, self.size).permute(1, 0)\n", "        x_pos = torch.sort(x, dim=1)[0]\n", "        y_eval = coef2curve(x_pos, self.grid, self.coef, self.k, device=self.device)\n", "        num_interval = self.grid.shape[1] - 1\n", "        ids = [int(batch / num_interval * i) for i in range(num_interval)] + [-1]\n", "        grid_adaptive = x_pos[:, ids]\n", "        margin = 0.01\n", "        grid_uniform = torch.cat([grid_adaptive[:, [0]] - margin + (grid_adaptive[:, [-1]] - grid_adaptive[:, [0]] + 2 * margin) * a for a in np.linspace(0, 1, num=self.grid.shape[1])], dim=1)\n", "        self.grid.data = self.grid_eps * grid_uniform + (1 - self.grid_eps) * grid_adaptive\n", "        self.coef.data = curve2coef(x_pos, y_eval, self.grid, self.k, device=self.device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### primary"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "class TextCNNBIGRU(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        \n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "        self.num_layers = 1\n", "        self.gru_hidden = 64\n", "        self.gru= nn.GRU(embedding_dim,self.gru_hidden,num_layers=self.num_layers,bidirectional=True)\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters + (self.gru_hidden * 2), output_dim)\n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        gru_output , hn = self.gru(embedded)\n", "\n", "        \n", "        gru_output = gru_output.permute(0,2,1)\n", "\n", "        # print(embedded.shape,gru_output.shape)  # torch.Size([32, 512, 128]) torch.Size([32, 512, 256])  #torch.Size([32, 60, 60]) torch.Size([32, 128, 60])\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "\n", "        conved.append(gru_output)\n", "        # print(conved[0].shape)\n", "\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "\n", "\n", "        return self.fc(cat)\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "        \n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "class TextCap(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.caps_net = CapsNet(\n", "            conv_in_ch=128,\n", "            conv_out_ch=32,\n", "            conv_kernel=3,\n", "            conv_stride=1,\n", "            prime_num_capsules=7,\n", "            prime_out_ch=1,\n", "            prime_kernel=3,\n", "            prime_stride=1,\n", "            secondary_num_capsules=7,\n", "            secondary_out_channels=32,\n", "            input_len=32,\n", "        )\n", "    def get_aspects_importances(self, text_embeddings):\n", "        \"\"\"Get aspect importances\n", "        \n", "        Args:\n", "            text_embedding: embeddings of a sentence as input\n", "        \n", "        Returns: \n", "            capsule weights, aspects_importances, weighted_text_emb\n", "\n", "        \"\"\"\n", "        # compute capsule scores, looking at text embeddings average\n", "        caps_weights = self.caps_net(text_embeddings.permute(0, 2, 1))\n", "\n", "        # multiplying text embeddings by attention scores -- and summing\n", "        # (matmul: we sum every word embedding's coordinate with attention weights)\n", "        weighted_text_emb = torch.matmul(caps_weights.unsqueeze(1),  # (batch, 1, sentence)\n", "                                         text_embeddings  # (batch, sentence, wv_dim)\n", "                                         ).squeeze()\n", "\n", "        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)\n", "        raw_importances = self.linear_transform(weighted_text_emb)\n", "\n", "        # computing 'aspects distribution in a sentence'\n", "        aspects_importances = self.softmax_aspects(raw_importances)\n", "\n", "        return caps_weights, aspects_importances, weighted_text_emb\n", "\n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "        \n", "        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(embedded)\n", "        \n", "        # print(aspects_importances.shape) ([32, 128]\n", "        return aspects_importances\n", "\n", "class Attention(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.attention = SelfAttention(embedding_dim, -1)\n", "        self.linear_transform = torch.nn.Linear(embedding_dim, 6)\n", "        self.softmax_aspects = torch.nn.Softmax(dim=1)\n", "        self.aspects_embeddings = Parameter(torch.empty(size=(embedding_dim, 6)))\n", "    def get_aspects_importances(self, text_embeddings):\n", "        \"\"\"Get aspect importances\n", "        \n", "        Args:\n", "            text_embedding: embeddings of a sentence as input\n", "        \n", "        Returns: \n", "            attention weights, aspects_importances, weighted_text_emb\n", "\n", "        \"\"\"\n", "        # compute attention scores, looking at text embeddings average\n", "        attention_weights = self.attention(text_embeddings)\n", "\n", "        # multiplying text embeddings by attention scores -- and summing\n", "        # (matmul: we sum every word embedding's coordinate with attention weights)\n", "        weighted_text_emb = torch.matmul(attention_weights.unsqueeze(1),  # (batch, 1, sentence)\n", "                                         text_embeddings  # (batch, sentence, wv_dim)\n", "                                         ).squeeze()\n", "        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)\n", "        # raw_importances = self.linear_transform(weighted_text_emb)\n", "        raw_importances = self.linear_transform(weighted_text_emb)\n", "\n", "        # computing 'aspects distribution in a sentence'\n", "        # print(\"raw_importances:\",raw_importances.shape)\n", "        aspects_importances = self.softmax_aspects(raw_importances)\n", "        # print(\"aspects_importances:\",aspects_importances.shape)\n", "\n", "        return attention_weights, aspects_importances, weighted_text_emb\n", "\n", "    def forward(self, text_embeddings):\n", "        \n", "        text_embeddings = self.embedding(text_embeddings)\n", "\n", "        # encoding: words embeddings -> sentence embedding, aspects importances\n", "        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(text_embeddings)\n", "        return aspects_importances\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 5, output_dim)\n", "\n", "        self.textcap = TextCap(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.attention = Attention(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.bigru_cnn = TextCNNBIGRU(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.bigru_cnn2 = TextCNNBIGRU(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "        text_output = self.bigru_cnn(text)\n", "        # text_output = self.textcnn(text)\n", "        # metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_text = self.bigru_cnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "        \n", "        attention_output = self.attention(text)\n", "        # metadata_attention_output = self.attention2(metadata_text)\n", "        fused_output = self.fuse(torch.cat((text_output,attention_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RUN"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["for num_epochs in [3,5,7,9,12,15,17,20]:\n", "    print(\"------------\",num_epochs)\n", "    vocab_size = 30522\n", "    embedding_dim = 128\n", "    n_filters = 128\n", "    filter_sizes = [3,4,5]\n", "    output_dim = 6\n", "    dropout = 0.5\n", "    padding_idx = 0\n", "    input_dim = 6 * metadata_each_dim\n", "    input_dim_metadata = 5\n", "    hidden_dim = 64\n", "    n_layers = 1\n", "    bidirectional = True\n", "\n", "    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "    # Define the optimizer and loss function\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "    # Record the training process\n", "    Train_acc = []\n", "    Train_loss = []\n", "    Train_macro_f1 = []\n", "    Train_micro_f1 = []\n", "\n", "    Val_acc = []\n", "    Val_loss = []\n", "    Val_macro_f1 = []\n", "    Val_micro_f1 = []\n", "\n", "    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "        epoch_trained = 0\n", "        train_label_all = []\n", "        train_predict_all = []\n", "        val_label_all = []\n", "        val_predict_all = []\n", "        best_valid_loss = float('inf')\n", "\n", "        start_time = time.time()\n", "        for epoch in range(num_epochs):\n", "            epoch_trained += 1\n", "            epoch_start_time = time.time()\n", "            # Training\n", "            model.train()\n", "            train_loss = 0.0\n", "            train_accuracy = 0.0\n", "            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                optimizer.zero_grad()\n", "                outputs = model(statements, metadata_text, metadata_number)\n", "                loss = criterion(outputs, label_onehot)\n", "                loss.backward()\n", "                optimizer.step()\n", "\n", "                train_loss += loss.item()\n", "                _, train_predicted = torch.max(outputs, 1)\n", "                train_accuracy += sum(train_predicted == label)\n", "                train_predict_all += train_predicted.tolist()\n", "                train_label_all += label.tolist()\n", "            train_loss /= len(train_loader)\n", "            train_accuracy /= len(train_loader.dataset)\n", "            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "            Train_acc.append(train_accuracy.tolist())\n", "            Train_loss.append(train_loss)\n", "            Train_macro_f1.append(train_macro_f1)\n", "            Train_micro_f1.append(train_micro_f1)\n", "\n", "            # Validation\n", "            model.eval()\n", "            val_loss = 0.0\n", "            val_accuracy = 0.0\n", "            with torch.no_grad():\n", "                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                    statements = statements.to(DEVICE)\n", "                    label_onehot = label_onehot.to(DEVICE)\n", "                    label = label.to(DEVICE)\n", "                    metadata_text = metadata_text.to(DEVICE)\n", "                    metadata_number = metadata_number.to(DEVICE)\n", "\n", "                    val_outputs = model(statements, metadata_text, metadata_number)\n", "                    val_loss += criterion(val_outputs, label_onehot).item()\n", "                    _, val_predicted = torch.max(val_outputs, 1)\n", "                    val_accuracy += sum(val_predicted == label)\n", "                    val_predict_all += val_predicted.tolist()\n", "                    val_label_all += label.tolist()\n", "            val_loss /= len(val_loader)\n", "            val_accuracy /= len(val_loader.dataset)\n", "            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "            Val_acc.append(val_accuracy.tolist())\n", "            Val_loss.append(val_loss)\n", "            Val_macro_f1.append(val_macro_f1)\n", "            Val_micro_f1.append(val_micro_f1)\n", "\n", "            if val_loss < best_valid_loss:\n", "                best_valid_loss = val_loss\n", "                torch.save(model.state_dict(), model_save)\n", "                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "            # Print the losses and accuracy\n", "            epoch_end_time = time.time()\n", "            epoch_time = epoch_end_time - epoch_start_time\n", "\n", "            print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "        end_time = time.time()\n", "        training_time = end_time - start_time\n", "        print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "    # Evaluate the model on new data\n", "    def test(model, test_loader, model_save):\n", "        model.load_state_dict(torch.load(model_save))\n", "        model.eval()\n", "\n", "        test_label_all = []\n", "        test_predict_all = []\n", "\n", "        test_loss = 0.0\n", "        test_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                test_outputs = model(statements, metadata_text, metadata_number)\n", "                test_loss += criterion(test_outputs, label_onehot).item()\n", "                _, test_predicted = torch.max(test_outputs, 1)\n", "                \n", "                test_accuracy += sum(test_predicted == label)\n", "                test_predict_all += test_predicted.tolist()\n", "                test_label_all += label.tolist()\n", "\n", "        test_loss /= len(test_loader)\n", "        test_accuracy /= len(test_loader.dataset)\n", "        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "    test(model, test_loader, model_save)"]}], "metadata": {"kernelspec": {"display_name": "py_39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}